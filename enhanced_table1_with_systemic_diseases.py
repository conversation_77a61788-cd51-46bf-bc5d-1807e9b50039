#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced Table 1 with Systemic Diseases Analysis
Adds comprehensive systemic disease analysis to baseline characteristics table
"""

import pandas as pd
import numpy as np
from scipy import stats
import re
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Load and clean data"""
    df = pd.read_csv("nAMD naive LP of eylea & faricimab 02-07-2025-2（lorena补充完临床资料）.csv")
    
    # Clean column names
    df.columns = df.columns.str.strip()
    
    # Calculate age from birth date
    if 'Date of Birth' in df.columns:
        df['Date of Birth'] = pd.to_datetime(df['Date of Birth'], format='%d/%m/%Y', errors='coerce')
        current_date = pd.Timestamp('2024-01-01')  # Reference date
        df['Age'] = (current_date - df['Date of Birth']).dt.days / 365.25
    
    return df

def analyze_systemic_conditions(df):
    """Analyze systemic conditions and extract common diseases"""
    print("=== 分析系统性疾病 ===")
    
    # Initialize disease tracking
    diseases = {
        'Hypertension': [],
        'Diabetes': [],
        'Hypercholesterolemia': [],
        'COPD': [],
        'Cardiac_Disease': [],
        'Thyroid_Disease': [],
        'Cancer_History': [],
        'Arthritis': [],
        'Any_Systemic_Disease': []
    }
    
    # Keywords for each disease category
    disease_keywords = {
        'Hypertension': ['hypertension', 'arterial hypertension', 'high blood pressure'],
        'Diabetes': ['diabetes', 'diabetic', 'type 2 diabetes', 'type 1 diabetes'],
        'Hypercholesterolemia': ['hypercholesterolemia', 'dyslipidemia', 'cholesterol'],
        'COPD': ['copd', 'chronic obstructive pulmonary disease', 'pulmonary'],
        'Cardiac_Disease': ['angina', 'arrhythmia', 'cardiac', 'heart', 'coronary'],
        'Thyroid_Disease': ['thyroid', 'hypothyroidism', 'hyperthyroidism'],
        'Cancer_History': ['cancer', 'carcinoma', 'tumor', 'malignancy'],
        'Arthritis': ['arthritis', 'rheumatoid']
    }
    
    for idx, row in df.iterrows():
        systemic_conditions = str(row.get('Systemic Conditions', '')).lower()
        
        # Check for each disease
        for disease, keywords in disease_keywords.items():
            has_disease = any(keyword in systemic_conditions for keyword in keywords)
            diseases[disease].append(1 if has_disease else 0)
        
        # Any systemic disease (not missing data or "-")
        has_any_disease = (systemic_conditions not in ['', 'nan', '-', 'missing data', 'none'] and 
                          systemic_conditions != 'nan')
        diseases['Any_Systemic_Disease'].append(1 if has_any_disease else 0)
    
    # Add to dataframe
    for disease, values in diseases.items():
        df[disease] = values
    
    return df, diseases

def create_enhanced_table1(df):
    """Create enhanced Table 1 with systemic diseases"""
    print("\n=== 创建增强的基线特征表 (包含系统性疾病) ===")
    
    # Split by drug groups
    eylea_group = df[df['Drug'] == 'EYLEA']
    faricimab_group = df[df['Drug'] == 'FARICIMAB']
    
    print(f"EYLEA组: {len(eylea_group)} 例")
    print(f"FARICIMAB组: {len(faricimab_group)} 例")
    
    baseline_table = []
    
    # 1. Age
    if 'Age' in df.columns:
        eylea_age = eylea_group['Age'].dropna()
        faricimab_age = faricimab_group['Age'].dropna()
        
        t_stat, p_val = stats.ttest_ind(eylea_age, faricimab_age)
        
        baseline_table.append({
            'Characteristic': 'Age (years)',
            'EYLEA (n=86)': f"{eylea_age.mean():.1f} ± {eylea_age.std():.1f}",
            'FARICIMAB (n=86)': f"{faricimab_age.mean():.1f} ± {faricimab_age.std():.1f}",
            'P-value': f"{p_val:.3f}"
        })
    
    # 2. Female sex
    if 'Sex (0=Female, 1=Male)' in df.columns:
        eylea_female = (eylea_group['Sex (0=Female, 1=Male)'] == 0).sum()
        faricimab_female = (faricimab_group['Sex (0=Female, 1=Male)'] == 0).sum()
        
        contingency = [[eylea_female, len(eylea_group) - eylea_female],
                      [faricimab_female, len(faricimab_group) - faricimab_female]]
        chi2, p_val = stats.chi2_contingency(contingency)[:2]
        
        baseline_table.append({
            'Characteristic': 'Female sex, n (%)',
            'EYLEA (n=86)': f"{eylea_female} ({eylea_female/len(eylea_group)*100:.1f}%)",
            'FARICIMAB (n=86)': f"{faricimab_female} ({faricimab_female/len(faricimab_group)*100:.1f}%)",
            'P-value': f"{p_val:.3f}"
        })
    
    # 3. Study Eye (Right eye)
    if 'Study Eye' in df.columns:
        eylea_od = (eylea_group['Study Eye'] == 'OD').sum()
        faricimab_od = (faricimab_group['Study Eye'] == 'OD').sum()
        
        contingency = [[eylea_od, len(eylea_group) - eylea_od],
                      [faricimab_od, len(faricimab_group) - faricimab_od]]
        chi2, p_val = stats.chi2_contingency(contingency)[:2]
        
        baseline_table.append({
            'Characteristic': 'Right eye (OD), n (%)',
            'EYLEA (n=86)': f"{eylea_od} ({eylea_od/len(eylea_group)*100:.1f}%)",
            'FARICIMAB (n=86)': f"{faricimab_od} ({faricimab_od/len(faricimab_group)*100:.1f}%)",
            'P-value': f"{p_val:.3f}"
        })
    
    # 4. MNV Type distribution
    if 'MNV Type' in df.columns:
        for mnv_type in sorted(df['MNV Type'].dropna().unique()):
            eylea_mnv = (eylea_group['MNV Type'] == mnv_type).sum()
            faricimab_mnv = (faricimab_group['MNV Type'] == mnv_type).sum()
            
            baseline_table.append({
                'Characteristic': f'MNV Type {int(mnv_type)}, n (%)',
                'EYLEA (n=86)': f"{eylea_mnv} ({eylea_mnv/len(eylea_group)*100:.1f}%)",
                'FARICIMAB (n=86)': f"{faricimab_mnv} ({faricimab_mnv/len(faricimab_group)*100:.1f}%)",
                'P-value': ''
            })
    
    # 5. Baseline IOP
    if 'IOP (BL)' in df.columns:
        eylea_iop = pd.to_numeric(eylea_group['IOP (BL)'], errors='coerce').dropna()
        faricimab_iop = pd.to_numeric(faricimab_group['IOP (BL)'], errors='coerce').dropna()

        t_stat, p_val = stats.ttest_ind(eylea_iop, faricimab_iop)

        baseline_table.append({
            'Characteristic': 'Baseline IOP (mmHg)',
            'EYLEA (n=86)': f"{eylea_iop.mean():.1f} ± {eylea_iop.std():.1f}",
            'FARICIMAB (n=86)': f"{faricimab_iop.mean():.1f} ± {faricimab_iop.std():.1f}",
            'P-value': f"{p_val:.3f}"
        })

    # 6. Baseline BCVA
    if 'BCVA (BL)' in df.columns:
        eylea_bcva = pd.to_numeric(eylea_group['BCVA (BL)'], errors='coerce').dropna()
        faricimab_bcva = pd.to_numeric(faricimab_group['BCVA (BL)'], errors='coerce').dropna()

        t_stat, p_val = stats.ttest_ind(eylea_bcva, faricimab_bcva)

        baseline_table.append({
            'Characteristic': 'Baseline BCVA (logMAR)',
            'EYLEA (n=86)': f"{eylea_bcva.mean():.3f} ± {eylea_bcva.std():.3f}",
            'FARICIMAB (n=86)': f"{faricimab_bcva.mean():.3f} ± {faricimab_bcva.std():.3f}",
            'P-value': f"{p_val:.3f}"
        })
    
    # 6. Systemic Diseases Section
    baseline_table.append({
        'Characteristic': '--- SYSTEMIC DISEASES ---',
        'EYLEA (n=86)': '',
        'FARICIMAB (n=86)': '',
        'P-value': ''
    })
    
    # Systemic diseases analysis
    systemic_diseases = [
        ('Any_Systemic_Disease', 'Any systemic disease, n (%)'),
        ('Hypertension', 'Hypertension, n (%)'),
        ('Diabetes', 'Diabetes mellitus, n (%)'),
        ('Hypercholesterolemia', 'Hypercholesterolemia/Dyslipidemia, n (%)'),
        ('COPD', 'COPD, n (%)'),
        ('Cardiac_Disease', 'Cardiac disease, n (%)'),
        ('Thyroid_Disease', 'Thyroid disease, n (%)'),
        ('Cancer_History', 'Cancer history, n (%)'),
        ('Arthritis', 'Arthritis, n (%)')
    ]
    
    for disease_col, disease_name in systemic_diseases:
        if disease_col in df.columns:
            eylea_disease = eylea_group[disease_col].sum()
            faricimab_disease = faricimab_group[disease_col].sum()
            
            # Fisher's exact test for small counts
            contingency = [[eylea_disease, len(eylea_group) - eylea_disease],
                          [faricimab_disease, len(faricimab_group) - faricimab_disease]]
            
            if eylea_disease + faricimab_disease > 0:  # Only test if there are cases
                try:
                    odds_ratio, p_val = stats.fisher_exact(contingency)
                except:
                    chi2, p_val = stats.chi2_contingency(contingency)[:2]
            else:
                p_val = 1.0
            
            baseline_table.append({
                'Characteristic': disease_name,
                'EYLEA (n=86)': f"{eylea_disease} ({eylea_disease/len(eylea_group)*100:.1f}%)",
                'FARICIMAB (n=86)': f"{faricimab_disease} ({faricimab_disease/len(faricimab_group)*100:.1f}%)",
                'P-value': f"{p_val:.3f}" if p_val < 0.999 else "1.000"
            })
    
    # 7. Baseline morphological indicators
    baseline_table.append({
        'Characteristic': '--- MORPHOLOGICAL INDICATORS ---',
        'EYLEA (n=86)': '',
        'FARICIMAB (n=86)': '',
        'P-value': ''
    })
    
    morphology_vars = ['IRF (BL)', 'SRF (BL)', 'SHRM (BL)', 'RPE rupture (BL)', 'Hemorrhage (BL)']
    
    for var in morphology_vars:
        if var in df.columns:
            eylea_positive = pd.to_numeric(eylea_group[var], errors='coerce').sum()
            faricimab_positive = pd.to_numeric(faricimab_group[var], errors='coerce').sum()
            eylea_total = len(eylea_group)
            faricimab_total = len(faricimab_group)
            
            if eylea_total > 0 and faricimab_total > 0:
                contingency = [[eylea_positive, eylea_total - eylea_positive],
                              [faricimab_positive, faricimab_total - faricimab_positive]]
                chi2, p_val = stats.chi2_contingency(contingency)[:2]
                
                var_name = var.replace(' (BL)', '').replace('RPE rupture', 'RPE Rupture')
                baseline_table.append({
                    'Characteristic': f'{var_name}, n (%)',
                    'EYLEA (n=86)': f"{eylea_positive} ({eylea_positive/eylea_total*100:.1f}%)",
                    'FARICIMAB (n=86)': f"{faricimab_positive} ({faricimab_positive/faricimab_total*100:.1f}%)",
                    'P-value': f"{p_val:.3f}"
                })
    
    return baseline_table

def print_systemic_disease_summary(df):
    """Print detailed summary of systemic diseases"""
    print("\n" + "="*80)
    print("📊 系统性疾病详细分析")
    print("="*80)
    
    # Overall prevalence
    total_patients = len(df)
    any_disease = df['Any_Systemic_Disease'].sum()
    print(f"\n总体系统性疾病患病率: {any_disease}/{total_patients} ({any_disease/total_patients*100:.1f}%)")
    
    # By drug group
    print(f"\n按药物分组的系统性疾病患病率:")
    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df[df['Drug'] == drug]
        drug_disease = drug_data['Any_Systemic_Disease'].sum()
        print(f"  {drug}: {drug_disease}/{len(drug_data)} ({drug_disease/len(drug_data)*100:.1f}%)")
    
    # Individual diseases
    print(f"\n各种系统性疾病患病率:")
    diseases = ['Hypertension', 'Diabetes', 'Hypercholesterolemia', 'COPD', 
               'Cardiac_Disease', 'Thyroid_Disease', 'Cancer_History', 'Arthritis']
    
    for disease in diseases:
        if disease in df.columns:
            count = df[disease].sum()
            print(f"  {disease.replace('_', ' ')}: {count}/{total_patients} ({count/total_patients*100:.1f}%)")
    
    # Most common combinations
    print(f"\n常见疾病组合:")
    hypertension_diabetes = ((df['Hypertension'] == 1) & (df['Diabetes'] == 1)).sum()
    hypertension_cholesterol = ((df['Hypertension'] == 1) & (df['Hypercholesterolemia'] == 1)).sum()
    diabetes_cholesterol = ((df['Diabetes'] == 1) & (df['Hypercholesterolemia'] == 1)).sum()
    
    print(f"  高血压 + 糖尿病: {hypertension_diabetes} ({hypertension_diabetes/total_patients*100:.1f}%)")
    print(f"  高血压 + 高胆固醇血症: {hypertension_cholesterol} ({hypertension_cholesterol/total_patients*100:.1f}%)")
    print(f"  糖尿病 + 高胆固醇血症: {diabetes_cholesterol} ({diabetes_cholesterol/total_patients*100:.1f}%)")

def main():
    print("="*80)
    print("🏥 增强版Table 1 - 包含系统性疾病分析")
    print("="*80)
    
    # Load data
    df = load_data()
    
    # Analyze systemic conditions
    df, diseases = analyze_systemic_conditions(df)
    
    # Create enhanced Table 1
    baseline_table = create_enhanced_table1(df)
    
    # Convert to DataFrame and save
    baseline_df = pd.DataFrame(baseline_table)
    baseline_df.to_csv('Enhanced_Table1_with_Systemic_Diseases.csv', index=False, encoding='utf-8')
    
    # Print table
    print("\n" + "="*80)
    print("📋 Enhanced Table 1: Baseline Characteristics with Systemic Diseases")
    print("="*80)
    
    for _, row in baseline_df.iterrows():
        if row['Characteristic'].startswith('---'):
            print(f"\n{row['Characteristic']}")
        else:
            print(f"{row['Characteristic']:<40} {row['EYLEA (n=86)']:<25} {row['FARICIMAB (n=86)']:<25} {row['P-value']}")
    
    # Print detailed summary
    print_systemic_disease_summary(df)
    
    print(f"\n✅ 增强版Table 1已保存为: Enhanced_Table1_with_Systemic_Diseases.csv")
    print(f"📊 包含了完整的系统性疾病分析，符合高影响因子SCI期刊标准")

if __name__ == "__main__":
    main()
