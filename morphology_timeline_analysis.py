#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Morphological Timeline Analysis for nAMD Study
Shows morphological indicators (IRF, SRF, SHRM, Hemorrhage) trends across three time points
with statistical comparisons between Eylea and Faricimab groups
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# Configure matplotlib for Adobe Acrobat editable fonts - FORCE SETTINGS
import matplotlib
# Force TrueType fonts for Adobe Acrobat compatibility
matplotlib.rcParams.update({
    'pdf.fonttype': 42,  # TrueType fonts (editable in Adobe)
    'ps.fonttype': 42,   # TrueType fonts for PostScript
    'font.family': 'sans-serif',
    'font.sans-serif': ['Arial', 'Helvetica', 'DejaVu Sans', 'Liberation Sans'],
    'font.size': 10,
    'axes.titlesize': 12,
    'axes.labelsize': 11,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 10,
    'axes.unicode_minus': False
})

# Set style for publication-quality plots
plt.style.use('default')
sns.set_palette("husl")

def calculate_proportion_ci(successes, total, confidence=0.95):
    """Calculate Wilson score interval for proportion confidence interval"""
    if total == 0:
        return 0, 0, 0
    
    p = successes / total
    z = stats.norm.ppf((1 + confidence) / 2)
    
    denominator = 1 + z**2 / total
    center = (p + z**2 / (2 * total)) / denominator
    margin = z * np.sqrt((p * (1 - p) + z**2 / (4 * total)) / total) / denominator
    
    lower = max(0, center - margin)
    upper = min(1, center + margin)
    
    return p, lower, upper

def calculate_fisher_exact_p(group1_pos, group1_total, group2_pos, group2_total):
    """Calculate Fisher's exact test p-value for two proportions"""
    if group1_total == 0 or group2_total == 0:
        return 1.0
    
    # Create contingency table
    contingency_table = [
        [group1_pos, group1_total - group1_pos],
        [group2_pos, group2_total - group2_pos]
    ]
    
    try:
        _, p_value = stats.fisher_exact(contingency_table)
        return p_value
    except:
        return 1.0

def format_p_value(p_value):
    """Format p-value with appropriate precision"""
    if p_value < 0.001:
        return "p<0.001"
    elif p_value < 0.01:
        return f"p={p_value:.3f}"
    else:
        return f"p={p_value:.2f}"

def get_significance_symbol(p_value):
    """Convert p-value to significance symbol"""
    if p_value < 0.001:
        return "***"
    elif p_value < 0.01:
        return "**"
    elif p_value < 0.05:
        return "*"
    else:
        return "ns"

def create_morphology_timeline_plot(df):
    """Create timeline plot showing morphological changes across three time points"""
    
    # Define morphological indicators and time points
    morphology_indicators = ['IRF', 'SRF', 'SHRM', 'Hemorrhage']
    time_points = ['BL', 'Post-LP', 'Year 1']
    time_labels = ['Baseline', 'Post-LP', '1-Year']
    
    # Prepare data structure
    results = {}
    
    for indicator in morphology_indicators:
        results[indicator] = {
            'EYLEA': {'proportions': [], 'lower_ci': [], 'upper_ci': [], 'counts': []},
            'FARICIMAB': {'proportions': [], 'lower_ci': [], 'upper_ci': [], 'counts': []},
            'p_values': []
        }
        
        for time_point in time_points:
            col_name = f"{indicator} ({time_point})"
            
            # Filter data for each drug group at this time point
            eylea_data = df[(df['Drug'] == 'EYLEA') & (df[col_name].notna())]
            faricimab_data = df[(df['Drug'] == 'FARICIMAB') & (df[col_name].notna())]
            
            # Calculate proportions for Eylea
            eylea_total = len(eylea_data)
            eylea_positive = len(eylea_data[eylea_data[col_name] == 1])
            eylea_prop, eylea_lower, eylea_upper = calculate_proportion_ci(eylea_positive, eylea_total)
            
            results[indicator]['EYLEA']['proportions'].append(eylea_prop)
            results[indicator]['EYLEA']['lower_ci'].append(eylea_lower)
            results[indicator]['EYLEA']['upper_ci'].append(eylea_upper)
            results[indicator]['EYLEA']['counts'].append(f"{eylea_positive}/{eylea_total}")

            # Calculate proportions for Faricimab
            faricimab_total = len(faricimab_data)
            faricimab_positive = len(faricimab_data[faricimab_data[col_name] == 1])
            faricimab_prop, faricimab_lower, faricimab_upper = calculate_proportion_ci(faricimab_positive, faricimab_total)

            results[indicator]['FARICIMAB']['proportions'].append(faricimab_prop)
            results[indicator]['FARICIMAB']['lower_ci'].append(faricimab_lower)
            results[indicator]['FARICIMAB']['upper_ci'].append(faricimab_upper)
            results[indicator]['FARICIMAB']['counts'].append(f"{faricimab_positive}/{faricimab_total}")
            
            # Calculate p-value for group comparison
            p_value = calculate_fisher_exact_p(eylea_positive, eylea_total, faricimab_positive, faricimab_total)
            results[indicator]['p_values'].append(p_value)
    
    # Create the plot
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.flatten()
    
    colors = {'EYLEA': '#2E86AB', 'FARICIMAB': '#A23B72'}
    
    for i, indicator in enumerate(morphology_indicators):
        ax = axes[i]
        
        # Plot lines for both groups
        for drug in ['EYLEA', 'FARICIMAB']:
            proportions = results[indicator][drug]['proportions']
            lower_ci = results[indicator][drug]['lower_ci']
            upper_ci = results[indicator][drug]['upper_ci']
            
            # Convert to percentages
            proportions_pct = [p * 100 for p in proportions]
            lower_ci_pct = [l * 100 for l in lower_ci]
            upper_ci_pct = [u * 100 for u in upper_ci]
            
            # Plot line with error bars
            ax.errorbar(range(len(time_labels)), proportions_pct, 
                       yerr=[np.array(proportions_pct) - np.array(lower_ci_pct),
                             np.array(upper_ci_pct) - np.array(proportions_pct)],
                       marker='o', linewidth=2.5, markersize=8, capsize=5,
                       label=drug, color=colors[drug], capthick=2)
            
            # Add count labels
            for j, (prop, count) in enumerate(zip(proportions_pct, results[indicator][drug]['counts'])):
                ax.annotate(count, (j, prop), textcoords="offset points",
                           xytext=(0,15 if drug == 'EYLEA' else -25), ha='center',
                           fontsize=9, color=colors[drug], weight='bold')
        
        # Add statistical significance annotations
        for j, p_value in enumerate(results[indicator]['p_values']):
            sig_symbol = get_significance_symbol(p_value)
            max_y = max(results[indicator]['EYLEA']['upper_ci'][j] * 100,
                       results[indicator]['FARICIMAB']['upper_ci'][j] * 100)
            
            ax.annotate(sig_symbol, (j, max_y + 5), ha='center', va='bottom',
                       fontsize=12, weight='bold', 
                       color='red' if p_value < 0.05 else 'gray')
            ax.annotate(format_p_value(p_value), (j, max_y + 10), ha='center', va='bottom',
                       fontsize=8, color='red' if p_value < 0.05 else 'gray')
        
        # Customize subplot
        ax.set_title(f'{indicator} Prevalence Over Time', fontsize=14, weight='bold', pad=20)
        ax.set_xlabel('Time Point', fontsize=12, weight='bold')
        ax.set_ylabel('Prevalence (%)', fontsize=12, weight='bold')
        ax.set_xticks(range(len(time_labels)))
        ax.set_xticklabels(time_labels)
        ax.set_ylim(-5, 105)
        ax.grid(True, alpha=0.3)
        ax.legend(loc='upper right', frameon=True, fancybox=True, shadow=True)
        
        # Add sample size information
        eylea_n = len(df[df['Drug'] == 'EYLEA'])
        faricimab_n = len(df[df['Drug'] == 'FARICIMAB'])
        ax.text(0.02, 0.98, f'EYLEA: n={eylea_n}\nFARICIMAB: n={faricimab_n}',
                transform=ax.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
                fontsize=10)
    
    plt.tight_layout()
    # Save as PNG, PDF, and SVG formats
    plt.savefig('Morphology_Timeline_Analysis.png', dpi=300, bbox_inches='tight')
    plt.savefig('Morphology_Timeline_Analysis.pdf', dpi=300, bbox_inches='tight')
    plt.savefig('Morphology_Timeline_Analysis.svg', dpi=300, bbox_inches='tight')
    plt.close()
    
    return results

def print_timeline_summary(results):
    """Print summary of timeline analysis results"""
    print("\n" + "="*80)
    print("MORPHOLOGICAL TIMELINE ANALYSIS SUMMARY")
    print("="*80)
    
    morphology_indicators = ['IRF', 'SRF', 'SHRM', 'Hemorrhage']
    time_labels = ['Baseline', 'Post-LP', '1-Year']
    
    for indicator in morphology_indicators:
        print(f"\n{indicator} Analysis:")
        print("-" * 50)
        
        for i, time_label in enumerate(time_labels):
            eylea_prop = results[indicator]['EYLEA']['proportions'][i] * 100
            eylea_count = results[indicator]['EYLEA']['counts'][i]
            faricimab_prop = results[indicator]['FARICIMAB']['proportions'][i] * 100
            faricimab_count = results[indicator]['FARICIMAB']['counts'][i]
            p_value = results[indicator]['p_values'][i]
            sig_symbol = get_significance_symbol(p_value)

            print(f"{time_label:10}: EYLEA {eylea_prop:5.1f}% ({eylea_count:>6}) vs "
                  f"FARICIMAB {faricimab_prop:5.1f}% ({faricimab_count:>6}) "
                  f"[{format_p_value(p_value)}, {sig_symbol}]")

# Main execution
if __name__ == "__main__":
    # Load data
    df = pd.read_csv('nAMD naive LP of eylea & faricimab 02-07-2025-2（lorena补充完临床资料）.csv')

    # Clean drug names - standardize different variations
    df['Drug'] = df['Drug'].str.strip()  # Remove leading/trailing spaces
    df['Drug'] = df['Drug'].str.upper()  # Convert to uppercase
    df.loc[df['Drug'].str.contains('EYLEA', na=False), 'Drug'] = 'EYLEA'
    df.loc[df['Drug'].str.contains('FARICIMAB', na=False), 'Drug'] = 'FARICIMAB'

    print("Drug distribution after cleaning:")
    print(df['Drug'].value_counts())
    print()

    print("Creating morphological timeline analysis...")
    results = create_morphology_timeline_plot(df)
    print_timeline_summary(results)

    print(f"\nTimeline analysis plot saved as 'Morphology_Timeline_Analysis.png/.pdf/.svg'")
    print("\nAnalysis completed successfully!")
