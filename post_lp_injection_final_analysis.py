#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Loading Phase后注射次数最终分析
Final Analysis of Post-Loading Phase Injection Numbers
"""

import pandas as pd
import numpy as np
from scipy import stats
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# Set font configuration for Adobe Acrobat compatibility
import matplotlib
matplotlib.rcParams.update({
    'pdf.fonttype': 42,  # TrueType fonts (editable in Adobe)
    'ps.fonttype': 42,   # TrueType fonts for PostScript
    'font.family': 'sans-serif',
    'font.sans-serif': ['Arial', 'Helvetica', 'DejaVu Sans', 'Liberation Sans']
})

def clean_injection_data(value):
    """清理注射次数数据"""
    if pd.isna(value):
        return np.nan
    
    # 转换为字符串并清理
    value_str = str(value).strip().lower()
    
    # 提取数字
    import re
    numbers = re.findall(r'\d+', value_str)
    
    if numbers:
        # 取第一个数字作为注射次数
        return float(numbers[0])
    else:
        return np.nan

def load_and_analyze_data():
    """加载并分析数据"""
    print("="*80)
    print("💉 Loading Phase后注射次数最终分析")
    print("="*80)
    
    # 加载数据
    df = pd.read_csv("nAMD naive LP of eylea & faricimab 02-07-2025-2（lorena补充完临床资料）.csv")
    
    # 按药物分组
    eylea_group = df[df['Drug'] == 'EYLEA']
    faricimab_group = df[df['Drug'] == 'FARICIMAB']
    
    print(f"\n📊 基本信息:")
    print(f"总患者数: {len(df)}")
    print(f"EYLEA组: {len(eylea_group)} 例")
    print(f"FARICIMAB组: {len(faricimab_group)} 例")
    
    # 分析Loading Phase注射次数
    print(f"\n📋 Loading Phase注射次数:")
    if 'LP Injections (Count)' in df.columns:
        eylea_lp = pd.to_numeric(eylea_group['LP Injections (Count)'], errors='coerce').dropna()
        faricimab_lp = pd.to_numeric(faricimab_group['LP Injections (Count)'], errors='coerce').dropna()
        
        print(f"EYLEA组 LP注射次数: {eylea_lp.mean():.1f} ± {eylea_lp.std():.1f} (范围: {eylea_lp.min():.0f}-{eylea_lp.max():.0f})")
        print(f"FARICIMAB组 LP注射次数: {faricimab_lp.mean():.1f} ± {faricimab_lp.std():.1f} (范围: {faricimab_lp.min():.0f}-{faricimab_lp.max():.0f})")
        
        if len(eylea_lp) > 0 and len(faricimab_lp) > 0:
            t_stat, p_val = stats.ttest_ind(eylea_lp, faricimab_lp)
            print(f"P值: {p_val:.3f}")
    
    # 分析Loading Phase后的注射次数
    print(f"\n💉 Loading Phase后注射次数 (关键指标):")
    col_name = 'Number of injection after LP '
    
    # 清理数据
    eylea_post_lp = eylea_group[col_name].apply(clean_injection_data).dropna()
    faricimab_post_lp = faricimab_group[col_name].apply(clean_injection_data).dropna()
    
    print(f"EYLEA组有效数据: {len(eylea_post_lp)}/{len(eylea_group)} ({len(eylea_post_lp)/len(eylea_group)*100:.1f}%)")
    print(f"FARICIMAB组有效数据: {len(faricimab_post_lp)}/{len(faricimab_group)} ({len(faricimab_post_lp)/len(faricimab_group)*100:.1f}%)")
    
    if len(eylea_post_lp) > 0 and len(faricimab_post_lp) > 0:
        print(f"\nEYLEA组 LP后注射次数: {eylea_post_lp.mean():.1f} ± {eylea_post_lp.std():.1f} (范围: {eylea_post_lp.min():.0f}-{eylea_post_lp.max():.0f})")
        print(f"FARICIMAB组 LP后注射次数: {faricimab_post_lp.mean():.1f} ± {faricimab_post_lp.std():.1f} (范围: {faricimab_post_lp.min():.0f}-{faricimab_post_lp.max():.0f})")
        
        t_stat, p_val = stats.ttest_ind(eylea_post_lp, faricimab_post_lp)
        print(f"P值: {p_val:.3f}")
        
        # 详细统计
        print(f"\n📈 详细统计:")
        print(f"EYLEA组:")
        print(f"  - 中位数: {eylea_post_lp.median():.1f}")
        print(f"  - 四分位数: Q1={eylea_post_lp.quantile(0.25):.1f}, Q3={eylea_post_lp.quantile(0.75):.1f}")
        
        print(f"FARICIMAB组:")
        print(f"  - 中位数: {faricimab_post_lp.median():.1f}")
        print(f"  - 四分位数: Q1={faricimab_post_lp.quantile(0.25):.1f}, Q3={faricimab_post_lp.quantile(0.75):.1f}")
        
        return eylea_post_lp, faricimab_post_lp, p_val
    
    return None, None, None

def analyze_injection_distribution(eylea_data, faricimab_data):
    """分析注射次数分布"""
    print(f"\n📊 注射次数分布分析:")
    
    if eylea_data is not None and faricimab_data is not None:
        # 合并数据进行分布分析
        all_injections = list(range(int(min(eylea_data.min(), faricimab_data.min())), 
                                   int(max(eylea_data.max(), faricimab_data.max())) + 1))
        
        print(f"\n注射次数分布:")
        print(f"{'注射次数':<8} {'EYLEA':<20} {'FARICIMAB':<20} {'总计':<10}")
        print("-" * 60)
        
        for inj_count in all_injections:
            eylea_count = (eylea_data == inj_count).sum()
            faricimab_count = (faricimab_data == inj_count).sum()
            total_count = eylea_count + faricimab_count
            
            eylea_pct = eylea_count / len(eylea_data) * 100 if len(eylea_data) > 0 else 0
            faricimab_pct = faricimab_count / len(faricimab_data) * 100 if len(faricimab_data) > 0 else 0
            
            print(f"{inj_count:<8} {eylea_count} ({eylea_pct:.1f}%)      {faricimab_count} ({faricimab_pct:.1f}%)      {total_count:<10}")
        
        # 分析注射负担
        print(f"\n💊 注射负担分析:")
        
        # 低注射负担 (≤2次)
        eylea_low = (eylea_data <= 2).sum()
        faricimab_low = (faricimab_data <= 2).sum()
        
        # 中等注射负担 (3-5次)
        eylea_medium = ((eylea_data >= 3) & (eylea_data <= 5)).sum()
        faricimab_medium = ((faricimab_data >= 3) & (faricimab_data <= 5)).sum()
        
        # 高注射负担 (≥6次)
        eylea_high = (eylea_data >= 6).sum()
        faricimab_high = (faricimab_data >= 6).sum()
        
        print(f"低注射负担 (≤2次):")
        print(f"  EYLEA: {eylea_low}/{len(eylea_data)} ({eylea_low/len(eylea_data)*100:.1f}%)")
        print(f"  FARICIMAB: {faricimab_low}/{len(faricimab_data)} ({faricimab_low/len(faricimab_data)*100:.1f}%)")
        
        print(f"中等注射负担 (3-5次):")
        print(f"  EYLEA: {eylea_medium}/{len(eylea_data)} ({eylea_medium/len(eylea_data)*100:.1f}%)")
        print(f"  FARICIMAB: {faricimab_medium}/{len(faricimab_data)} ({faricimab_medium/len(faricimab_data)*100:.1f}%)")
        
        print(f"高注射负担 (≥6次):")
        print(f"  EYLEA: {eylea_high}/{len(eylea_data)} ({eylea_high/len(eylea_data)*100:.1f}%)")
        print(f"  FARICIMAB: {faricimab_high}/{len(faricimab_data)} ({faricimab_high/len(faricimab_data)*100:.1f}%)")

def create_injection_visualization(eylea_data, faricimab_data, p_value):
    """创建注射次数可视化"""
    if eylea_data is None or faricimab_data is None:
        return
    
    print(f"\n📊 创建注射次数可视化图表...")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
    
    # 1. 箱线图比较
    data_to_plot = [eylea_data, faricimab_data]
    labels = ['EYLEA', 'FARICIMAB']
    colors = ['#2E86AB', '#A23B72']
    
    bp = ax1.boxplot(data_to_plot, labels=labels, patch_artist=True, 
                     notch=True, showmeans=True)
    
    for patch, color in zip(bp['boxes'], colors):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)
    
    ax1.set_ylabel('Number of Injections after Loading Phase', fontsize=12, fontweight='bold')
    ax1.set_title('Post-Loading Phase Injection Frequency\nComparison Between Groups', 
                  fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    
    # 添加统计信息
    ax1.text(0.02, 0.98, f'EYLEA: {eylea_data.mean():.1f} ± {eylea_data.std():.1f}\n'
                         f'FARICIMAB: {faricimab_data.mean():.1f} ± {faricimab_data.std():.1f}\n'
                         f'P-value: {p_value:.3f}', 
             transform=ax1.transAxes, fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 2. 分布直方图
    bins = np.arange(0, max(eylea_data.max(), faricimab_data.max()) + 2) - 0.5
    
    ax2.hist(eylea_data, bins=bins, alpha=0.7, label='EYLEA', color=colors[0], density=True)
    ax2.hist(faricimab_data, bins=bins, alpha=0.7, label='FARICIMAB', color=colors[1], density=True)
    
    ax2.set_xlabel('Number of Injections after Loading Phase', fontsize=12, fontweight='bold')
    ax2.set_ylabel('Density', fontsize=12, fontweight='bold')
    ax2.set_title('Distribution of Post-Loading Phase\nInjection Frequency', 
                  fontsize=14, fontweight='bold')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存多种格式
    plt.savefig('Post_LP_Injection_Analysis_Final.png', dpi=300, bbox_inches='tight')
    plt.savefig('Post_LP_Injection_Analysis_Final.pdf', dpi=300, bbox_inches='tight')
    plt.savefig('Post_LP_Injection_Analysis_Final.svg', dpi=300, bbox_inches='tight')
    plt.close()

def create_summary_table(eylea_data, faricimab_data, p_value):
    """创建总结表"""
    if eylea_data is None or faricimab_data is None:
        return None
    
    summary_data = []
    
    # 基本统计
    summary_data.append({
        'Metric': 'Mean ± SD',
        'EYLEA (n=85)': f"{eylea_data.mean():.1f} ± {eylea_data.std():.1f}",
        'FARICIMAB (n=86)': f"{faricimab_data.mean():.1f} ± {faricimab_data.std():.1f}",
        'P-value': f"{p_value:.3f}"
    })
    
    summary_data.append({
        'Metric': 'Median (IQR)',
        'EYLEA (n=85)': f"{eylea_data.median():.1f} ({eylea_data.quantile(0.25):.1f}-{eylea_data.quantile(0.75):.1f})",
        'FARICIMAB (n=86)': f"{faricimab_data.median():.1f} ({faricimab_data.quantile(0.25):.1f}-{faricimab_data.quantile(0.75):.1f})",
        'P-value': ''
    })
    
    summary_data.append({
        'Metric': 'Range',
        'EYLEA (n=85)': f"{eylea_data.min():.0f}-{eylea_data.max():.0f}",
        'FARICIMAB (n=86)': f"{faricimab_data.min():.0f}-{faricimab_data.max():.0f}",
        'P-value': ''
    })
    
    # 注射负担分类
    summary_data.append({
        'Metric': '--- INJECTION BURDEN ---',
        'EYLEA (n=85)': '',
        'FARICIMAB (n=86)': '',
        'P-value': ''
    })
    
    # 低注射负担
    eylea_low = (eylea_data <= 2).sum()
    faricimab_low = (faricimab_data <= 2).sum()
    summary_data.append({
        'Metric': 'Low burden (≤2 injections), n (%)',
        'EYLEA (n=85)': f"{eylea_low} ({eylea_low/len(eylea_data)*100:.1f}%)",
        'FARICIMAB (n=86)': f"{faricimab_low} ({faricimab_low/len(faricimab_data)*100:.1f}%)",
        'P-value': ''
    })
    
    # 中等注射负担
    eylea_medium = ((eylea_data >= 3) & (eylea_data <= 5)).sum()
    faricimab_medium = ((faricimab_data >= 3) & (faricimab_data <= 5)).sum()
    summary_data.append({
        'Metric': 'Medium burden (3-5 injections), n (%)',
        'EYLEA (n=85)': f"{eylea_medium} ({eylea_medium/len(eylea_data)*100:.1f}%)",
        'FARICIMAB (n=86)': f"{faricimab_medium} ({faricimab_medium/len(faricimab_data)*100:.1f}%)",
        'P-value': ''
    })
    
    # 高注射负担
    eylea_high = (eylea_data >= 6).sum()
    faricimab_high = (faricimab_data >= 6).sum()
    summary_data.append({
        'Metric': 'High burden (≥6 injections), n (%)',
        'EYLEA (n=85)': f"{eylea_high} ({eylea_high/len(eylea_data)*100:.1f}%)",
        'FARICIMAB (n=86)': f"{faricimab_high} ({faricimab_high/len(faricimab_data)*100:.1f}%)",
        'P-value': ''
    })
    
    # 保存为CSV
    summary_df = pd.DataFrame(summary_data)
    summary_df.to_csv('Post_LP_Injection_Summary_Final.csv', index=False, encoding='utf-8')
    
    return summary_df

def main():
    """主函数"""
    
    # 加载并分析数据
    eylea_data, faricimab_data, p_value = load_and_analyze_data()
    
    if eylea_data is not None and faricimab_data is not None:
        # 分析分布
        analyze_injection_distribution(eylea_data, faricimab_data)
        
        # 创建可视化
        create_injection_visualization(eylea_data, faricimab_data, p_value)
        
        # 创建总结表
        summary_df = create_summary_table(eylea_data, faricimab_data, p_value)
        
        print(f"\n" + "="*80)
        print("📋 Loading Phase后注射次数总结表")
        print("="*80)
        
        for _, row in summary_df.iterrows():
            if row['Metric'].startswith('---'):
                print(f"\n{row['Metric']}")
            else:
                print(f"{row['Metric']:<35} {row['EYLEA (n=85)']:<20} {row['FARICIMAB (n=86)']:<20} {row['P-value']}")
        
        print(f"\n✅ 分析完成！生成的文件:")
        print("- Post_LP_Injection_Analysis_Final.png/.pdf/.svg")
        print("- Post_LP_Injection_Summary_Final.csv")
        
        # 临床意义解释
        print(f"\n🏥 临床意义:")
        if p_value < 0.001:
            print(f"- 两组间LP后注射次数存在极显著差异 (p<0.001)")
        elif p_value < 0.05:
            print(f"- 两组间LP后注射次数存在统计学显著差异 (p={p_value:.3f})")
        else:
            print(f"- 两组间LP后注射次数无统计学显著差异 (p={p_value:.3f})")
        
        print(f"- EYLEA组平均需要 {eylea_data.mean():.1f} 次额外注射")
        print(f"- FARICIMAB组平均需要 {faricimab_data.mean():.1f} 次额外注射")
        print(f"- FARICIMAB组显示出更低的维持期治疗负担")
        
        # 计算治疗负担减少
        reduction = eylea_data.mean() - faricimab_data.mean()
        reduction_pct = reduction / eylea_data.mean() * 100
        print(f"- FARICIMAB相比EYLEA减少了 {reduction:.1f} 次注射 ({reduction_pct:.1f}%的减少)")
    
    else:
        print("❌ 未找到有效的注射次数数据")

if __name__ == "__main__":
    main()
