#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to generate PDF with editable fonts for Adobe Acrobat
"""

import matplotlib
import matplotlib.pyplot as plt
import numpy as np

# Force TrueType fonts for Adobe Acrobat compatibility
matplotlib.rcParams['pdf.fonttype'] = 42  # TrueType fonts
matplotlib.rcParams['ps.fonttype'] = 42   # TrueType fonts for PostScript
matplotlib.rcParams['font.family'] = 'sans-serif'
matplotlib.rcParams['font.sans-serif'] = ['Arial', 'Helvetica', 'DejaVu Sans']

def test_editable_pdf():
    """Create a test PDF with editable fonts"""
    
    # Create sample data
    categories = ['IRF', 'SRF', 'SHRM', 'Hemorrhage']
    eylea_values = [25.5, 76.7, 86.0, 15.1]
    faricimab_values = [30.2, 82.6, 77.9, 44.2]
    
    x = np.arange(len(categories))
    width = 0.35
    
    # Create figure
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Create bars
    bars1 = ax.bar(x - width/2, eylea_values, width, label='EYLEA', 
                   color='#2E86AB', alpha=0.8)
    bars2 = ax.bar(x + width/2, faricimab_values, width, label='FARICIMAB', 
                   color='#A23B72', alpha=0.8)
    
    # Customize plot
    ax.set_xlabel('Morphological Indicators', fontsize=14, fontweight='bold')
    ax.set_ylabel('Prevalence (%)', fontsize=14, fontweight='bold')
    ax.set_title('Test PDF with Editable Fonts - nAMD Morphology Analysis', 
                 fontsize=16, fontweight='bold', pad=20)
    ax.set_xticks(x)
    ax.set_xticklabels(categories, fontsize=12)
    ax.legend(fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar in bars1:
        height = bar.get_height()
        ax.annotate(f'{height:.1f}%',
                   xy=(bar.get_x() + bar.get_width() / 2, height),
                   xytext=(0, 3),  # 3 points vertical offset
                   textcoords="offset points",
                   ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    for bar in bars2:
        height = bar.get_height()
        ax.annotate(f'{height:.1f}%',
                   xy=(bar.get_x() + bar.get_width() / 2, height),
                   xytext=(0, 3),  # 3 points vertical offset
                   textcoords="offset points",
                   ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    # Add text annotation
    ax.text(0.02, 0.98, 'Font Type: TrueType (fonttype=42)\nEditable in Adobe Acrobat', 
            transform=ax.transAxes, verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8),
            fontsize=10)
    
    plt.tight_layout()
    
    # Save with explicit font settings
    plt.savefig('Test_Editable_PDF.pdf', dpi=300, bbox_inches='tight',
                metadata={'Creator': 'matplotlib with TrueType fonts', 
                         'Producer': 'matplotlib',
                         'Title': 'Test Editable PDF'})
    plt.savefig('Test_Editable_PDF.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ Test PDF generated: Test_Editable_PDF.pdf")
    print("📊 Font settings:")
    print(f"   - pdf.fonttype: {matplotlib.rcParams['pdf.fonttype']}")
    print(f"   - ps.fonttype: {matplotlib.rcParams['ps.fonttype']}")
    print(f"   - font.family: {matplotlib.rcParams['font.family']}")
    print(f"   - font.sans-serif: {matplotlib.rcParams['font.sans-serif'][:3]}...")
    
    return True

if __name__ == "__main__":
    print("="*60)
    print("测试Adobe Acrobat可编辑PDF生成")
    print("="*60)
    
    test_editable_pdf()
    
    print("\n🎯 验证方法:")
    print("1. 在Adobe Acrobat中打开Test_Editable_PDF.pdf")
    print("2. 使用文本编辑工具选择文字")
    print("3. 检查是否可以编辑标题和标签")
    print("4. 文字应该可以选择、复制和修改")
    
    print("\n📝 如果文字可编辑，说明字体设置正确！")
