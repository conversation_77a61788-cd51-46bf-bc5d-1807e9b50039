#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Revised Table 1 Analysis with IOP and Refined Systemic Disease Classification
重新分析系统性疾病分类，确保统计方法的准确性
"""

import pandas as pd
import numpy as np
from scipy import stats
import re
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Load and clean data"""
    df = pd.read_csv("nAMD naive LP of eylea & faricimab 02-07-2025-2（lorena补充完临床资料）.csv")
    
    # Clean column names
    df.columns = df.columns.str.strip()
    
    # Calculate age from birth date
    if 'Date of Birth' in df.columns:
        df['Date of Birth'] = pd.to_datetime(df['Date of Birth'], format='%d/%m/%Y', errors='coerce')
        current_date = pd.Timestamp('2024-01-01')  # Reference date
        df['Age'] = (current_date - df['Date of Birth']).dt.days / 365.25
    
    return df

def analyze_systemic_conditions_refined(df):
    """重新分析系统性疾病，使用更精确的分类方法"""
    print("=== 重新分析系统性疾病分类 ===")
    
    # 打印所有系统性疾病条目以便检查
    print("\n所有系统性疾病条目:")
    unique_conditions = df['Systemic Conditions'].dropna().unique()
    for i, condition in enumerate(unique_conditions, 1):
        print(f"{i:2d}. {condition}")
    
    # 初始化疾病追踪
    diseases = {
        'Hypertension': [],
        'Diabetes': [],
        'Hypercholesterolemia': [],
        'COPD': [],
        'Cardiac_Disease': [],
        'Thyroid_Disease': [],
        'Cancer_History': [],
        'Arthritis': [],
        'Any_Systemic_Disease': []
    }
    
    # 更精确的关键词匹配
    disease_keywords = {
        'Hypertension': ['hypertension', 'arterial hypertension', 'high blood pressure', 'htn'],
        'Diabetes': ['diabetes', 'diabetic', 'type 2 diabetes', 'type 1 diabetes', 'dm', 't2dm', 't1dm'],
        'Hypercholesterolemia': ['hypercholesterolemia', 'dyslipidemia', 'cholesterol', 'lipid'],
        'COPD': ['copd', 'chronic obstructive pulmonary disease', 'pulmonary', 'respiratory'],
        'Cardiac_Disease': ['angina', 'arrhythmia', 'cardiac', 'heart', 'coronary', 'cardiovascular', 'atrial'],
        'Thyroid_Disease': ['thyroid', 'hypothyroidism', 'hyperthyroidism', 'thyro'],
        'Cancer_History': ['cancer', 'carcinoma', 'tumor', 'malignancy', 'oncology', 'neoplasm'],
        'Arthritis': ['arthritis', 'rheumatoid', 'joint', 'arthritic']
    }
    
    # 分析每个患者的系统性疾病
    detailed_analysis = []
    
    for idx, row in df.iterrows():
        patient_name = row.get('Name', f'Patient_{idx}')
        drug = row.get('Drug', 'Unknown')
        systemic_conditions = str(row.get('Systemic Conditions', '')).lower()
        
        patient_diseases = {}
        
        # 检查每种疾病
        for disease, keywords in disease_keywords.items():
            has_disease = any(keyword in systemic_conditions for keyword in keywords)
            diseases[disease].append(1 if has_disease else 0)
            patient_diseases[disease] = has_disease
        
        # 任何系统性疾病（排除缺失数据）
        has_any_disease = (systemic_conditions not in ['', 'nan', '-', 'missing data', 'none'] and 
                          systemic_conditions != 'nan' and
                          len(systemic_conditions.strip()) > 0)
        diseases['Any_Systemic_Disease'].append(1 if has_any_disease else 0)
        patient_diseases['Any_Systemic_Disease'] = has_any_disease
        
        # 记录详细分析
        detailed_analysis.append({
            'Patient': patient_name,
            'Drug': drug,
            'Original_Conditions': row.get('Systemic Conditions', ''),
            'Processed_Conditions': systemic_conditions,
            **patient_diseases
        })
    
    # 添加到数据框
    for disease, values in diseases.items():
        df[disease] = values
    
    # 保存详细分析
    detailed_df = pd.DataFrame(detailed_analysis)
    detailed_df.to_csv('Detailed_Systemic_Disease_Analysis.csv', index=False, encoding='utf-8')
    
    return df, diseases, detailed_df

def create_revised_table1(df):
    """创建修订版Table 1，包含IOP"""
    print("\n=== 创建修订版基线特征表 (包含IOP) ===")
    
    # 按药物分组
    eylea_group = df[df['Drug'] == 'EYLEA']
    faricimab_group = df[df['Drug'] == 'FARICIMAB']
    
    print(f"EYLEA组: {len(eylea_group)} 例")
    print(f"FARICIMAB组: {len(faricimab_group)} 例")
    
    baseline_table = []
    
    # 1. Age
    if 'Age' in df.columns:
        eylea_age = eylea_group['Age'].dropna()
        faricimab_age = faricimab_group['Age'].dropna()
        
        t_stat, p_val = stats.ttest_ind(eylea_age, faricimab_age)
        
        baseline_table.append({
            'Characteristic': 'Age (years)',
            'EYLEA (n=85)': f"{eylea_age.mean():.1f} ± {eylea_age.std():.1f}",
            'FARICIMAB (n=86)': f"{faricimab_age.mean():.1f} ± {faricimab_age.std():.1f}",
            'P-value': f"{p_val:.3f}"
        })
    
    # 2. Female sex
    if 'Sex (0=Female, 1=Male)' in df.columns:
        eylea_female = (eylea_group['Sex (0=Female, 1=Male)'] == 0).sum()
        faricimab_female = (faricimab_group['Sex (0=Female, 1=Male)'] == 0).sum()
        
        contingency = [[eylea_female, len(eylea_group) - eylea_female],
                      [faricimab_female, len(faricimab_group) - faricimab_female]]
        chi2, p_val = stats.chi2_contingency(contingency)[:2]
        
        baseline_table.append({
            'Characteristic': 'Female sex, n (%)',
            'EYLEA (n=85)': f"{eylea_female} ({eylea_female/len(eylea_group)*100:.1f}%)",
            'FARICIMAB (n=86)': f"{faricimab_female} ({faricimab_female/len(faricimab_group)*100:.1f}%)",
            'P-value': f"{p_val:.3f}"
        })
    
    # 3. Study Eye (Right eye)
    if 'Study Eye' in df.columns:
        eylea_od = (eylea_group['Study Eye'] == 'OD').sum()
        faricimab_od = (faricimab_group['Study Eye'] == 'OD').sum()
        
        contingency = [[eylea_od, len(eylea_group) - eylea_od],
                      [faricimab_od, len(faricimab_group) - faricimab_od]]
        chi2, p_val = stats.chi2_contingency(contingency)[:2]
        
        baseline_table.append({
            'Characteristic': 'Right eye (OD), n (%)',
            'EYLEA (n=85)': f"{eylea_od} ({eylea_od/len(eylea_group)*100:.1f}%)",
            'FARICIMAB (n=86)': f"{faricimab_od} ({faricimab_od/len(faricimab_group)*100:.1f}%)",
            'P-value': f"{p_val:.3f}"
        })
    
    # 4. MNV Type distribution
    if 'MNV Type' in df.columns:
        for mnv_type in sorted(df['MNV Type'].dropna().unique()):
            eylea_mnv = (eylea_group['MNV Type'] == mnv_type).sum()
            faricimab_mnv = (faricimab_group['MNV Type'] == mnv_type).sum()
            
            baseline_table.append({
                'Characteristic': f'MNV Type {int(mnv_type)}, n (%)',
                'EYLEA (n=85)': f"{eylea_mnv} ({eylea_mnv/len(eylea_group)*100:.1f}%)",
                'FARICIMAB (n=86)': f"{faricimab_mnv} ({faricimab_mnv/len(faricimab_group)*100:.1f}%)",
                'P-value': ''
            })
    
    # 5. Baseline IOP (新增)
    if 'IOP (BL)' in df.columns:
        eylea_iop = pd.to_numeric(eylea_group['IOP (BL)'], errors='coerce').dropna()
        faricimab_iop = pd.to_numeric(faricimab_group['IOP (BL)'], errors='coerce').dropna()
        
        if len(eylea_iop) > 0 and len(faricimab_iop) > 0:
            t_stat, p_val = stats.ttest_ind(eylea_iop, faricimab_iop)
            
            baseline_table.append({
                'Characteristic': 'Baseline IOP (mmHg)',
                'EYLEA (n=85)': f"{eylea_iop.mean():.1f} ± {eylea_iop.std():.1f}",
                'FARICIMAB (n=86)': f"{faricimab_iop.mean():.1f} ± {faricimab_iop.std():.1f}",
                'P-value': f"{p_val:.3f}"
            })
    
    # 6. Baseline BCVA
    if 'BCVA (BL)' in df.columns:
        eylea_bcva = pd.to_numeric(eylea_group['BCVA (BL)'], errors='coerce').dropna()
        faricimab_bcva = pd.to_numeric(faricimab_group['BCVA (BL)'], errors='coerce').dropna()
        
        t_stat, p_val = stats.ttest_ind(eylea_bcva, faricimab_bcva)
        
        baseline_table.append({
            'Characteristic': 'Baseline BCVA (logMAR)',
            'EYLEA (n=85)': f"{eylea_bcva.mean():.3f} ± {eylea_bcva.std():.3f}",
            'FARICIMAB (n=86)': f"{faricimab_bcva.mean():.3f} ± {faricimab_bcva.std():.3f}",
            'P-value': f"{p_val:.3f}"
        })
    
    # 7. 系统性疾病部分
    baseline_table.append({
        'Characteristic': '--- SYSTEMIC DISEASES ---',
        'EYLEA (n=85)': '',
        'FARICIMAB (n=86)': '',
        'P-value': ''
    })
    
    # 系统性疾病分析
    systemic_diseases = [
        ('Any_Systemic_Disease', 'Any systemic disease, n (%)'),
        ('Hypertension', 'Hypertension, n (%)'),
        ('Cardiac_Disease', 'Cardiac disease, n (%)'),
        ('Diabetes', 'Diabetes mellitus, n (%)'),
        ('Hypercholesterolemia', 'Hypercholesterolemia/Dyslipidemia, n (%)'),
        ('COPD', 'COPD, n (%)'),
        ('Thyroid_Disease', 'Thyroid disease, n (%)'),
        ('Cancer_History', 'Cancer history, n (%)'),
        ('Arthritis', 'Arthritis, n (%)')
    ]
    
    for disease_col, disease_name in systemic_diseases:
        if disease_col in df.columns:
            eylea_disease = eylea_group[disease_col].sum()
            faricimab_disease = faricimab_group[disease_col].sum()
            
            # 使用Fisher精确检验或卡方检验
            contingency = [[eylea_disease, len(eylea_group) - eylea_disease],
                          [faricimab_disease, len(faricimab_group) - faricimab_disease]]
            
            if eylea_disease + faricimab_disease > 0:  # 只有在有病例时才进行检验
                try:
                    odds_ratio, p_val = stats.fisher_exact(contingency)
                except:
                    chi2, p_val = stats.chi2_contingency(contingency)[:2]
            else:
                p_val = 1.0
            
            baseline_table.append({
                'Characteristic': disease_name,
                'EYLEA (n=85)': f"{eylea_disease} ({eylea_disease/len(eylea_group)*100:.1f}%)",
                'FARICIMAB (n=86)': f"{faricimab_disease} ({faricimab_disease/len(faricimab_group)*100:.1f}%)",
                'P-value': f"{p_val:.3f}" if p_val < 0.999 else "1.000"
            })
    
    # 8. 基线形态学指标
    baseline_table.append({
        'Characteristic': '--- MORPHOLOGICAL INDICATORS ---',
        'EYLEA (n=85)': '',
        'FARICIMAB (n=86)': '',
        'P-value': ''
    })
    
    morphology_vars = ['IRF (BL)', 'SRF (BL)', 'SHRM (BL)', 'RPE rupture (BL)', 'Hemorrhage (BL)']
    
    for var in morphology_vars:
        if var in df.columns:
            eylea_positive = pd.to_numeric(eylea_group[var], errors='coerce').sum()
            faricimab_positive = pd.to_numeric(faricimab_group[var], errors='coerce').sum()
            eylea_total = len(eylea_group)
            faricimab_total = len(faricimab_group)
            
            if eylea_total > 0 and faricimab_total > 0:
                contingency = [[eylea_positive, eylea_total - eylea_positive],
                              [faricimab_positive, faricimab_total - faricimab_positive]]
                chi2, p_val = stats.chi2_contingency(contingency)[:2]
                
                var_name = var.replace(' (BL)', '').replace('RPE rupture', 'RPE Rupture')
                baseline_table.append({
                    'Characteristic': f'{var_name}, n (%)',
                    'EYLEA (n=85)': f"{eylea_positive} ({eylea_positive/eylea_total*100:.1f}%)",
                    'FARICIMAB (n=86)': f"{faricimab_positive} ({faricimab_positive/faricimab_total*100:.1f}%)",
                    'P-value': f"{p_val:.3f}"
                })
    
    return baseline_table

def main():
    print("="*80)
    print("🏥 修订版Table 1 - 包含IOP和精确的系统性疾病分析")
    print("="*80)
    
    # 加载数据
    df = load_data()
    
    # 重新分析系统性疾病
    df, diseases, detailed_df = analyze_systemic_conditions_refined(df)
    
    # 创建修订版Table 1
    baseline_table = create_revised_table1(df)
    
    # 转换为DataFrame并保存
    baseline_df = pd.DataFrame(baseline_table)
    baseline_df.to_csv('Revised_Table1_with_IOP.csv', index=False, encoding='utf-8')
    
    # 打印表格
    print("\n" + "="*80)
    print("📋 Revised Table 1: Baseline Characteristics with IOP")
    print("="*80)
    
    for _, row in baseline_df.iterrows():
        if row['Characteristic'].startswith('---'):
            print(f"\n{row['Characteristic']}")
        else:
            print(f"{row['Characteristic']:<40} {row['EYLEA (n=85)']:<25} {row['FARICIMAB (n=86)']:<25} {row['P-value']}")
    
    print(f"\n✅ 修订版Table 1已保存为: Revised_Table1_with_IOP.csv")
    print(f"📊 详细系统性疾病分析已保存为: Detailed_Systemic_Disease_Analysis.csv")
    print(f"🔍 请检查详细分析文件以验证疾病分类的准确性")

if __name__ == "__main__":
    main()
