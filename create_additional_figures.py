#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Additional Figures for nAMD Analysis
Create publication-quality figures for SCI journals

Author: AI Assistant
Date: 2025-01-07
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import scipy.stats as stats
from scipy.stats import ttest_ind
import warnings
warnings.filterwarnings('ignore')

# Set English fonts
plt.rcParams['font.family'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

# Set figure style
sns.set_style("whitegrid")
sns.set_palette("Set2")

def load_data():
    """Load cleaned data"""
    df = pd.read_csv('namd_cleaned_data.csv')
    useful_cols = [col for col in df.columns if not col.startswith('Unnamed')]
    return df[useful_cols]

def create_bcva_change_boxplot(df):
    """Create BCVA change boxplot"""
    print("Creating BCVA change boxplot...")
    
    # Calculate BCVA changes
    df['BCVA_Change_PostLP'] = pd.to_numeric(df['BCVA (BL)'], errors='coerce') - pd.to_numeric(df['BCVA (Post-LP)'], errors='coerce')
    
    # Prepare data for plotting
    plot_data = []
    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df[df['Drug'] == drug]['BCVA_Change_PostLP'].dropna()
        for value in drug_data:
            plot_data.append({'Drug': drug, 'BCVA_Change': value})
    
    plot_df = pd.DataFrame(plot_data)
    
    # Create figure
    fig, ax = plt.subplots(figsize=(8, 6))
    
    # Create boxplot
    box_plot = sns.boxplot(data=plot_df, x='Drug', y='BCVA_Change', ax=ax, 
                          palette=['#2E86AB', '#A23B72'])
    
    # Add individual points
    sns.stripplot(data=plot_df, x='Drug', y='BCVA_Change', ax=ax, 
                 color='black', alpha=0.5, size=3)
    
    # Statistical test
    eylea_changes = df[df['Drug'] == 'EYLEA']['BCVA_Change_PostLP'].dropna()
    faricimab_changes = df[df['Drug'] == 'FARICIMAB']['BCVA_Change_PostLP'].dropna()
    t_stat, p_val = ttest_ind(eylea_changes, faricimab_changes)
    
    # Add statistical annotation
    y_max = plot_df['BCVA_Change'].max()
    y_min = plot_df['BCVA_Change'].min()
    y_range = y_max - y_min
    
    ax.plot([0, 1], [y_max + 0.05*y_range, y_max + 0.05*y_range], 'k-', linewidth=1)
    ax.plot([0, 0], [y_max + 0.03*y_range, y_max + 0.05*y_range], 'k-', linewidth=1)
    ax.plot([1, 1], [y_max + 0.03*y_range, y_max + 0.05*y_range], 'k-', linewidth=1)
    
    if p_val < 0.001:
        p_text = 'p < 0.001'
    elif p_val < 0.01:
        p_text = 'p < 0.01'
    elif p_val < 0.05:
        p_text = 'p < 0.05'
    else:
        p_text = f'p = {p_val:.3f}'
    
    ax.text(0.5, y_max + 0.07*y_range, p_text, ha='center', va='bottom', fontsize=12)
    
    # Set labels and title
    ax.set_xlabel('Treatment Group', fontsize=12)
    ax.set_ylabel('BCVA Change from Baseline (logMAR)', fontsize=12)
    ax.set_title('BCVA Change After Loading Phase\n(Positive values indicate improvement)', 
                fontsize=14, fontweight='bold')
    
    # Add horizontal line at zero
    ax.axhline(y=0, color='red', linestyle='--', alpha=0.7, linewidth=1)
    
    # Add sample sizes
    for i, drug in enumerate(['EYLEA', 'FARICIMAB']):
        n = len(df[df['Drug'] == drug]['BCVA_Change_PostLP'].dropna())
        ax.text(i, y_min - 0.1*y_range, f'n = {n}', ha='center', va='top', fontsize=10)
    
    plt.tight_layout()
    plt.savefig('Figure2_BCVA_Change_Boxplot.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("BCVA change boxplot saved as: Figure2_BCVA_Change_Boxplot.png")

def create_morphology_heatmap(df):
    """Create morphological indicators heatmap"""
    print("Creating morphological indicators heatmap...")
    
    # Load morphological analysis results
    morphology_df = pd.read_csv('Morphological_Analysis_Results.csv')
    
    # Prepare data for heatmap
    variables = ['IRF', 'SRF', 'SHRM', 'Hemorrhage']  # Exclude RPE rupture due to low prevalence
    timepoints = ['BL', 'Post-LP', 'Year 1']
    
    # Create matrices for EYLEA and FARICIMAB
    eylea_matrix = np.zeros((len(variables), len(timepoints)))
    faricimab_matrix = np.zeros((len(variables), len(timepoints)))
    
    for i, var in enumerate(variables):
        for j, tp in enumerate(timepoints):
            # Find corresponding row in morphology_df
            row = morphology_df[(morphology_df['Variable'] == var) & 
                              (morphology_df['Timepoint'] == tp)]
            if not row.empty:
                eylea_matrix[i, j] = row.iloc[0]['EYLEA_percent']
                faricimab_matrix[i, j] = row.iloc[0]['FARICIMAB_percent']
            else:
                eylea_matrix[i, j] = np.nan
                faricimab_matrix[i, j] = np.nan
    
    # Create figure with subplots
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
    
    # EYLEA heatmap
    sns.heatmap(eylea_matrix, annot=True, fmt='.1f', cmap='Reds', 
                xticklabels=timepoints, yticklabels=variables,
                ax=ax1, cbar_kws={'label': 'Prevalence (%)'})
    ax1.set_title('EYLEA\nMorphological Indicators', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Timepoint', fontsize=12)
    ax1.set_ylabel('Morphological Indicator', fontsize=12)
    
    # FARICIMAB heatmap
    sns.heatmap(faricimab_matrix, annot=True, fmt='.1f', cmap='Blues',
                xticklabels=timepoints, yticklabels=variables,
                ax=ax2, cbar_kws={'label': 'Prevalence (%)'})
    ax2.set_title('FARICIMAB\nMorphological Indicators', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Timepoint', fontsize=12)
    ax2.set_ylabel('')
    
    plt.tight_layout()
    plt.savefig('Figure3_Morphology_Heatmap.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Morphological heatmap saved as: Figure3_Morphology_Heatmap.png")

def create_baseline_characteristics_plot(df):
    """Create baseline characteristics comparison plot"""
    print("Creating baseline characteristics plot...")
    
    # Prepare data for key baseline characteristics
    characteristics = []
    
    # Age
    eylea_age = df[df['Drug'] == 'EYLEA']['Age'].dropna()
    faricimab_age = df[df['Drug'] == 'FARICIMAB']['Age'].dropna()
    characteristics.append({
        'Characteristic': 'Age (years)',
        'EYLEA_mean': eylea_age.mean(),
        'EYLEA_std': eylea_age.std(),
        'FARICIMAB_mean': faricimab_age.mean(),
        'FARICIMAB_std': faricimab_age.std()
    })
    
    # Baseline BCVA
    eylea_bcva = pd.to_numeric(df[df['Drug'] == 'EYLEA']['BCVA (BL)'], errors='coerce').dropna()
    faricimab_bcva = pd.to_numeric(df[df['Drug'] == 'FARICIMAB']['BCVA (BL)'], errors='coerce').dropna()
    characteristics.append({
        'Characteristic': 'Baseline BCVA (logMAR)',
        'EYLEA_mean': eylea_bcva.mean(),
        'EYLEA_std': eylea_bcva.std(),
        'FARICIMAB_mean': faricimab_bcva.mean(),
        'FARICIMAB_std': faricimab_bcva.std()
    })
    
    # Create figure
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    
    # Age comparison
    age_data = [eylea_age, faricimab_age]
    bp1 = axes[0].boxplot(age_data, labels=['EYLEA', 'FARICIMAB'], patch_artist=True)
    bp1['boxes'][0].set_facecolor('#2E86AB')
    bp1['boxes'][1].set_facecolor('#A23B72')
    axes[0].set_title('Age Distribution', fontsize=14, fontweight='bold')
    axes[0].set_ylabel('Age (years)', fontsize=12)
    
    # BCVA comparison
    bcva_data = [eylea_bcva, faricimab_bcva]
    bp2 = axes[1].boxplot(bcva_data, labels=['EYLEA', 'FARICIMAB'], patch_artist=True)
    bp2['boxes'][0].set_facecolor('#2E86AB')
    bp2['boxes'][1].set_facecolor('#A23B72')
    axes[1].set_title('Baseline BCVA Distribution', fontsize=14, fontweight='bold')
    axes[1].set_ylabel('BCVA (logMAR)', fontsize=12)
    
    plt.tight_layout()
    plt.savefig('Figure4_Baseline_Characteristics.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Baseline characteristics plot saved as: Figure4_Baseline_Characteristics.png")

def create_summary_table():
    """Create a comprehensive summary table"""
    print("Creating comprehensive summary table...")
    
    # Load analysis results
    bcva_df = pd.read_csv('BCVA_Analysis_Results.csv')
    morphology_df = pd.read_csv('Morphological_Analysis_Results.csv')
    baseline_df = pd.read_csv('Table1_Baseline_Characteristics.csv')
    
    # Create summary
    summary_data = []
    
    # Add BCVA results
    for _, row in bcva_df.iterrows():
        summary_data.append({
            'Category': 'BCVA',
            'Parameter': f"{row['Timepoint']} BCVA (logMAR)",
            'EYLEA': f"{row['EYLEA_mean']:.3f} ± {row['EYLEA_std']:.3f} (n={row['EYLEA_n']})",
            'FARICIMAB': f"{row['FARICIMAB_mean']:.3f} ± {row['FARICIMAB_std']:.3f} (n={row['FARICIMAB_n']})",
            'P_value': f"{row['P_value']:.3f}"
        })
    
    # Add key morphological results (baseline and post-LP)
    key_morphology = morphology_df[morphology_df['Timepoint'].isin(['BL', 'Post-LP'])]
    for _, row in key_morphology.iterrows():
        summary_data.append({
            'Category': 'Morphology',
            'Parameter': f"{row['Variable']} ({row['Timepoint']})",
            'EYLEA': f"{row['EYLEA_positive']}/{row['EYLEA_total']} ({row['EYLEA_percent']:.1f}%)",
            'FARICIMAB': f"{row['FARICIMAB_positive']}/{row['FARICIMAB_total']} ({row['FARICIMAB_percent']:.1f}%)",
            'P_value': f"{row['P_value']:.3f}" if not pd.isna(row['P_value']) else 'N/A'
        })
    
    # Save summary table
    summary_df = pd.DataFrame(summary_data)
    summary_df.to_csv('Table2_Comprehensive_Summary.csv', index=False)
    
    print("Comprehensive summary table saved as: Table2_Comprehensive_Summary.csv")

if __name__ == "__main__":
    # Load data
    df = load_data()
    
    # Create additional figures
    create_bcva_change_boxplot(df)
    create_morphology_heatmap(df)
    create_baseline_characteristics_plot(df)
    create_summary_table()
    
    print("\nAll additional figures and tables created successfully!")
    print("Generated files:")
    print("- Figure2_BCVA_Change_Boxplot.png")
    print("- Figure3_Morphology_Heatmap.png") 
    print("- Figure4_Baseline_Characteristics.png")
    print("- Table2_Comprehensive_Summary.csv")
