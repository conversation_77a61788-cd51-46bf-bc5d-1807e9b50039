#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
nAMD疗效分析和可视化
主要疗效终点分析和图表生成

Author: AI Assistant
Date: 2025-01-07
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import scipy.stats as stats
from scipy.stats import ttest_ind, chi2_contingency
import warnings
warnings.filterwarnings('ignore')

# 设置英文字体
plt.rcParams['font.family'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

# 设置图形样式
sns.set_style("whitegrid")
sns.set_palette("Set2")

def load_cleaned_data():
    """加载清洗后的数据"""
    df = pd.read_csv('namd_cleaned_data.csv')
    # 只保留有效列
    useful_cols = [col for col in df.columns if not col.startswith('Unnamed')]
    return df[useful_cols]

def analyze_bcva_changes(df):
    """Analyze BCVA changes"""
    print("=== BCVA Efficacy Analysis ===")

    # Check available BCVA columns
    available_bcva_cols = [col for col in df.columns if 'BCVA' in col]
    print(f"Available BCVA columns: {available_bcva_cols}")

    # Convert BCVA data to numeric
    for col in available_bcva_cols:
        df[col] = pd.to_numeric(df[col], errors='coerce')

    # Calculate BCVA changes (only for available columns)
    if 'BCVA (Post-LP)' in df.columns:
        df['BCVA_Change_PostLP'] = df['BCVA (BL)'] - df['BCVA (Post-LP)']  # Positive values indicate improvement
    
    # 分组分析
    eylea_group = df[df['Drug'] == 'EYLEA']
    faricimab_group = df[df['Drug'] == 'FARICIMAB']
    
    results = []
    
    # 基线BCVA
    eylea_bl = eylea_group['BCVA (BL)'].dropna()
    faricimab_bl = faricimab_group['BCVA (BL)'].dropna()
    t_stat, p_val = ttest_ind(eylea_bl, faricimab_bl)
    
    results.append({
        'Timepoint': 'Baseline',
        'EYLEA_mean': eylea_bl.mean(),
        'EYLEA_std': eylea_bl.std(),
        'EYLEA_n': len(eylea_bl),
        'FARICIMAB_mean': faricimab_bl.mean(),
        'FARICIMAB_std': faricimab_bl.std(),
        'FARICIMAB_n': len(faricimab_bl),
        'P_value': p_val
    })
    
    # Post-LP BCVA
    eylea_postlp = eylea_group['BCVA (Post-LP)'].dropna()
    faricimab_postlp = faricimab_group['BCVA (Post-LP)'].dropna()
    if len(eylea_postlp) > 0 and len(faricimab_postlp) > 0:
        t_stat, p_val = ttest_ind(eylea_postlp, faricimab_postlp)
        
        results.append({
            'Timepoint': 'Post-LP',
            'EYLEA_mean': eylea_postlp.mean(),
            'EYLEA_std': eylea_postlp.std(),
            'EYLEA_n': len(eylea_postlp),
            'FARICIMAB_mean': faricimab_postlp.mean(),
            'FARICIMAB_std': faricimab_postlp.std(),
            'FARICIMAB_n': len(faricimab_postlp),
            'P_value': p_val
        })
    
    # Year 1 BCVA (如果存在)
    if 'BCVA (Year 1)' in df.columns:
        eylea_y1 = eylea_group['BCVA (Year 1)'].dropna()
        faricimab_y1 = faricimab_group['BCVA (Year 1)'].dropna()
        if len(eylea_y1) > 0 and len(faricimab_y1) > 0:
            t_stat, p_val = ttest_ind(eylea_y1, faricimab_y1)

            results.append({
                'Timepoint': 'Year 1',
                'EYLEA_mean': eylea_y1.mean(),
                'EYLEA_std': eylea_y1.std(),
                'EYLEA_n': len(eylea_y1),
                'FARICIMAB_mean': faricimab_y1.mean(),
                'FARICIMAB_std': faricimab_y1.std(),
                'FARICIMAB_n': len(faricimab_y1),
                'P_value': p_val
            })
    
    # BCVA change analysis
    print("\nBCVA Change Analysis:")

    # Post-LP changes
    eylea_change_postlp = eylea_group['BCVA_Change_PostLP'].dropna()
    faricimab_change_postlp = faricimab_group['BCVA_Change_PostLP'].dropna()
    if len(eylea_change_postlp) > 0 and len(faricimab_change_postlp) > 0:
        t_stat, p_val = ttest_ind(eylea_change_postlp, faricimab_change_postlp)
        print(f"Post-LP BCVA Change (logMAR):")
        print(f"  EYLEA: {eylea_change_postlp.mean():.3f} ± {eylea_change_postlp.std():.3f} (n={len(eylea_change_postlp)})")
        print(f"  FARICIMAB: {faricimab_change_postlp.mean():.3f} ± {faricimab_change_postlp.std():.3f} (n={len(faricimab_change_postlp)})")
        print(f"  P-value: {p_val:.3f}")

    # Year 1 changes (if available)
    if 'BCVA_Change_Year1' in df.columns:
        eylea_change_y1 = eylea_group['BCVA_Change_Year1'].dropna()
        faricimab_change_y1 = faricimab_group['BCVA_Change_Year1'].dropna()
        if len(eylea_change_y1) > 0 and len(faricimab_change_y1) > 0:
            t_stat, p_val = ttest_ind(eylea_change_y1, faricimab_change_y1)
            print(f"\nYear 1 BCVA Change (logMAR):")
            print(f"  EYLEA: {eylea_change_y1.mean():.3f} ± {eylea_change_y1.std():.3f} (n={len(eylea_change_y1)})")
            print(f"  FARICIMAB: {faricimab_change_y1.mean():.3f} ± {faricimab_change_y1.std():.3f} (n={len(faricimab_change_y1)})")
            print(f"  P-value: {p_val:.3f}")
    
    # 保存结果
    results_df = pd.DataFrame(results)
    results_df.to_csv('BCVA_Analysis_Results.csv', index=False)
    
    return df, results_df

def create_bcva_trend_plot(df):
    """Create BCVA trend plot"""
    print("\nCreating BCVA trend plot...")

    # Check available BCVA columns
    available_bcva_cols = [col for col in df.columns if 'BCVA' in col and col != 'BCVA_Change_PostLP']
    print(f"Available BCVA columns: {available_bcva_cols}")

    # Prepare data - only use available columns
    if 'BCVA (Year 1)' in df.columns:
        timepoints = ['Baseline', 'Post-LP', 'Year 1']
        bcva_cols = ['BCVA (BL)', 'BCVA (Post-LP)', 'BCVA (Year 1)']
    else:
        timepoints = ['Baseline', 'Post-LP']
        bcva_cols = ['BCVA (BL)', 'BCVA (Post-LP)']
    
    # Calculate means and standard errors for each timepoint
    eylea_means = []
    eylea_sems = []
    faricimab_means = []
    faricimab_sems = []

    for col in bcva_cols:
        if col in df.columns:
            eylea_data = pd.to_numeric(df[df['Drug'] == 'EYLEA'][col], errors='coerce').dropna()
            faricimab_data = pd.to_numeric(df[df['Drug'] == 'FARICIMAB'][col], errors='coerce').dropna()

            eylea_means.append(eylea_data.mean())
            eylea_sems.append(eylea_data.std() / np.sqrt(len(eylea_data)))
            faricimab_means.append(faricimab_data.mean())
            faricimab_sems.append(faricimab_data.std() / np.sqrt(len(faricimab_data)))
        else:
            eylea_means.append(np.nan)
            eylea_sems.append(np.nan)
            faricimab_means.append(np.nan)
            faricimab_sems.append(np.nan)

    # Create figure
    fig, ax = plt.subplots(figsize=(10, 6))

    x = np.arange(len(timepoints))
    width = 0.35

    # Create bar plot
    bars1 = ax.bar(x - width/2, eylea_means, width, yerr=eylea_sems,
                   label='EYLEA', alpha=0.8, capsize=5, color='#2E86AB')
    bars2 = ax.bar(x + width/2, faricimab_means, width, yerr=faricimab_sems,
                   label='FARICIMAB', alpha=0.8, capsize=5, color='#A23B72')

    # Set labels and title
    ax.set_xlabel('Timepoint', fontsize=12)
    ax.set_ylabel('BCVA (logMAR)', fontsize=12)
    ax.set_title('BCVA Changes Over Time\n(Lower values indicate better vision)', fontsize=14, fontweight='bold')
    ax.set_xticks(x)
    ax.set_xticklabels(timepoints)
    ax.legend()

    # Add value labels
    for i, (bar1, bar2) in enumerate(zip(bars1, bars2)):
        if not np.isnan(eylea_means[i]):
            ax.text(bar1.get_x() + bar1.get_width()/2, bar1.get_height() + eylea_sems[i] + 0.01,
                   f'{eylea_means[i]:.3f}', ha='center', va='bottom', fontsize=10)
        if not np.isnan(faricimab_means[i]):
            ax.text(bar2.get_x() + bar2.get_width()/2, bar2.get_height() + faricimab_sems[i] + 0.01,
                   f'{faricimab_means[i]:.3f}', ha='center', va='bottom', fontsize=10)

    plt.tight_layout()
    plt.savefig('Figure1_BCVA_Trend.png', dpi=300, bbox_inches='tight')
    plt.close()  # Close the figure to free memory

    print("BCVA trend plot saved as: Figure1_BCVA_Trend.png")

def analyze_morphological_changes(df):
    """Analyze morphological indicator changes"""
    print("\n=== Morphological Analysis ===")

    morphology_vars = ['IRF', 'SRF', 'SHRM', 'RPE rupture', 'Hemorrhage']
    timepoints = ['BL', 'Post-LP', 'Year 1']

    results = []

    for var in morphology_vars:
        for tp in timepoints:
            col_name = f"{var} ({tp})"
            if col_name in df.columns:
                eylea_data = pd.to_numeric(df[df['Drug'] == 'EYLEA'][col_name], errors='coerce')
                faricimab_data = pd.to_numeric(df[df['Drug'] == 'FARICIMAB'][col_name], errors='coerce')

                eylea_positive = eylea_data.sum()
                eylea_total = eylea_data.notna().sum()
                faricimab_positive = faricimab_data.sum()
                faricimab_total = faricimab_data.notna().sum()

                # Chi-square test
                if eylea_total > 0 and faricimab_total > 0:
                    contingency = [[eylea_positive, eylea_total - eylea_positive],
                                  [faricimab_positive, faricimab_total - faricimab_positive]]
                    try:
                        chi2, p_val = chi2_contingency(contingency)[:2]
                    except:
                        p_val = np.nan

                    results.append({
                        'Variable': var,
                        'Timepoint': tp,
                        'EYLEA_positive': eylea_positive,
                        'EYLEA_total': eylea_total,
                        'EYLEA_percent': eylea_positive/eylea_total*100 if eylea_total > 0 else 0,
                        'FARICIMAB_positive': faricimab_positive,
                        'FARICIMAB_total': faricimab_total,
                        'FARICIMAB_percent': faricimab_positive/faricimab_total*100 if faricimab_total > 0 else 0,
                        'P_value': p_val
                    })

    # Save results
    morphology_df = pd.DataFrame(results)
    morphology_df.to_csv('Morphological_Analysis_Results.csv', index=False)

    # Print key results
    print("\nMorphological Analysis Results:")
    for _, row in morphology_df.iterrows():
        if not np.isnan(row['P_value']):
            print(f"{row['Variable']} ({row['Timepoint']}):")
            print(f"  EYLEA: {row['EYLEA_positive']}/{row['EYLEA_total']} ({row['EYLEA_percent']:.1f}%)")
            print(f"  FARICIMAB: {row['FARICIMAB_positive']}/{row['FARICIMAB_total']} ({row['FARICIMAB_percent']:.1f}%)")
            print(f"  P-value: {row['P_value']:.3f}")
            print()

    return morphology_df

if __name__ == "__main__":
    # Load data
    df = load_cleaned_data()

    # BCVA analysis
    df_with_changes, bcva_results = analyze_bcva_changes(df)

    # Create BCVA trend plot
    create_bcva_trend_plot(df_with_changes)

    # Morphological analysis
    morphology_results = analyze_morphological_changes(df_with_changes)

    print("\nAnalysis completed! Generated files:")
    print("- BCVA_Analysis_Results.csv")
    print("- Morphological_Analysis_Results.csv")
    print("- Figure1_BCVA_Trend.png")
