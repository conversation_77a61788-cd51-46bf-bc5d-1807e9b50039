#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
nAMD Real-World Study Analysis
比较Eylea和Faricimab在nAMD治疗中的真实世界疗效分析

Author: AI Assistant
Date: 2025-01-07
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import scipy.stats as stats
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置图形样式
sns.set_style("whitegrid")
plt.style.use('seaborn-v0_8')

def load_and_clean_data(filepath):
    """
    加载并清洗数据
    """
    print("正在加载数据...")
    
    # 读取CSV文件
    df = pd.read_csv(filepath, encoding='utf-8')
    
    print(f"原始数据形状: {df.shape}")
    print(f"列名: {list(df.columns)}")
    
    # 显示前几行数据
    print("\n前5行数据:")
    print(df.head())
    
    return df

def clean_data(df):
    """
    数据清洗函数
    """
    print("\n开始数据清洗...")
    
    # 创建数据副本
    df_clean = df.copy()
    
    # 1. 处理列名（去除空格和特殊字符）
    df_clean.columns = df_clean.columns.str.strip()
    
    # 2. 检查数据类型
    print("\n数据类型:")
    print(df_clean.dtypes)
    
    # 3. 检查缺失值
    print("\n缺失值统计:")
    missing_stats = df_clean.isnull().sum()
    missing_percent = (missing_stats / len(df_clean)) * 100
    missing_df = pd.DataFrame({
        'Missing_Count': missing_stats,
        'Missing_Percent': missing_percent
    })
    print(missing_df[missing_df['Missing_Count'] > 0])
    
    # 4. 处理日期列
    date_columns = ['Date of Birth', 'Date of First Injection', 'Date of Last LP Injection', 'Date (Year 1)']
    
    for col in date_columns:
        if col in df_clean.columns:
            print(f"\n处理日期列: {col}")
            # 检查无效日期
            invalid_dates = df_clean[col].str.contains('31/02|30/02|31/04|31/06|31/09|31/11', na=False)
            if invalid_dates.any():
                print(f"发现 {invalid_dates.sum()} 个无效日期")
                # 将无效日期设为NaN
                df_clean.loc[invalid_dates, col] = np.nan
    
    # 5. 计算年龄
    if 'Date of Birth' in df_clean.columns:
        try:
            df_clean['Date of Birth'] = pd.to_datetime(df_clean['Date of Birth'], format='%d/%m/%Y', errors='coerce')
            df_clean['Age'] = (datetime.now() - df_clean['Date of Birth']).dt.days / 365.25
            df_clean['Age'] = df_clean['Age'].round(1)
        except:
            print("年龄计算失败")
    
    # 6. 处理性别列
    if 'Sex (0=Female, 1=Male)' in df_clean.columns:
        df_clean['Sex'] = df_clean['Sex (0=Female, 1=Male)'].map({0: 'Female', 1: 'Male'})
    
    # 7. 标准化药物名称
    if 'Drug' in df_clean.columns:
        df_clean['Drug'] = df_clean['Drug'].str.strip().str.upper()
        print(f"\n药物分布:")
        print(df_clean['Drug'].value_counts())
    
    # 8. 检查重复患者
    if 'Name' in df_clean.columns:
        duplicates = df_clean['Name'].duplicated(keep=False)
        if duplicates.any():
            print(f"\n发现 {duplicates.sum()} 个重复患者记录:")
            print(df_clean[duplicates][['Name', 'Study Eye', 'Drug']].sort_values('Name'))
    
    # 9. 创建唯一标识符
    df_clean['Patient_ID'] = df_clean['Name'] + '_' + df_clean['Study Eye'].astype(str)
    
    print(f"\n清洗后数据形状: {df_clean.shape}")
    
    return df_clean

def basic_statistics(df):
    """
    基础统计分析
    """
    print("\n=== 基础统计分析 ===")
    
    # 总体统计
    print(f"总患者数: {len(df)}")
    print(f"药物分组:")
    drug_counts = df['Drug'].value_counts()
    for drug, count in drug_counts.items():
        print(f"  {drug}: {count} ({count/len(df)*100:.1f}%)")
    
    # 年龄统计
    if 'Age' in df.columns:
        print(f"\n年龄统计:")
        print(f"  平均年龄: {df['Age'].mean():.1f} ± {df['Age'].std():.1f} 岁")
        print(f"  年龄范围: {df['Age'].min():.1f} - {df['Age'].max():.1f} 岁")
    
    # 性别分布
    if 'Sex' in df.columns:
        print(f"\n性别分布:")
        sex_counts = df['Sex'].value_counts()
        for sex, count in sex_counts.items():
            print(f"  {sex}: {count} ({count/len(df)*100:.1f}%)")
    
    # MNV类型分布
    if 'MNV Type' in df.columns:
        print(f"\nMNV类型分布:")
        mnv_counts = df['MNV Type'].value_counts().sort_index()
        for mnv_type, count in mnv_counts.items():
            print(f"  Type {mnv_type}: {count} ({count/len(df)*100:.1f}%)")
    
    # 基线BCVA统计
    if 'BCVA (BL)' in df.columns:
        bcva_bl = pd.to_numeric(df['BCVA (BL)'], errors='coerce')
        print(f"\n基线BCVA (logMAR):")
        print(f"  平均值: {bcva_bl.mean():.3f} ± {bcva_bl.std():.3f}")
        print(f"  中位数: {bcva_bl.median():.3f}")
        print(f"  范围: {bcva_bl.min():.3f} - {bcva_bl.max():.3f}")

def create_baseline_table(df):
    """
    创建基线特征比较表 (Table 1)
    """
    print("\n=== 创建基线特征比较表 ===")

    # 只保留有效的列
    useful_cols = [col for col in df.columns if not col.startswith('Unnamed')]
    df_analysis = df[useful_cols].copy()

    # 分组
    eylea_group = df_analysis[df_analysis['Drug'] == 'EYLEA']
    faricimab_group = df_analysis[df_analysis['Drug'] == 'FARICIMAB']

    print(f"EYLEA组: {len(eylea_group)} 例")
    print(f"FARICIMAB组: {len(faricimab_group)} 例")

    # 创建基线特征表
    baseline_table = []

    # 1. 年龄
    if 'Age' in df_analysis.columns:
        eylea_age = eylea_group['Age'].dropna()
        faricimab_age = faricimab_group['Age'].dropna()

        # t检验
        t_stat, p_val = stats.ttest_ind(eylea_age, faricimab_age)

        baseline_table.append({
            'Characteristic': 'Age (years)',
            'EYLEA (n=86)': f"{eylea_age.mean():.1f} ± {eylea_age.std():.1f}",
            'FARICIMAB (n=86)': f"{faricimab_age.mean():.1f} ± {faricimab_age.std():.1f}",
            'P-value': f"{p_val:.3f}"
        })

    # 2. 性别
    if 'Sex' in df_analysis.columns:
        eylea_female = (eylea_group['Sex'] == 'Female').sum()
        faricimab_female = (faricimab_group['Sex'] == 'Female').sum()

        # 卡方检验
        contingency = [[eylea_female, len(eylea_group) - eylea_female],
                      [faricimab_female, len(faricimab_group) - faricimab_female]]
        chi2, p_val = stats.chi2_contingency(contingency)[:2]

        baseline_table.append({
            'Characteristic': 'Female sex, n (%)',
            'EYLEA (n=86)': f"{eylea_female} ({eylea_female/len(eylea_group)*100:.1f}%)",
            'FARICIMAB (n=86)': f"{faricimab_female} ({faricimab_female/len(faricimab_group)*100:.1f}%)",
            'P-value': f"{p_val:.3f}"
        })

    # 3. 研究眼分布
    if 'Study Eye' in df_analysis.columns:
        eylea_od = (eylea_group['Study Eye'].str.upper() == 'OD').sum()
        faricimab_od = (faricimab_group['Study Eye'].str.upper() == 'OD').sum()

        contingency = [[eylea_od, len(eylea_group) - eylea_od],
                      [faricimab_od, len(faricimab_group) - faricimab_od]]
        chi2, p_val = stats.chi2_contingency(contingency)[:2]

        baseline_table.append({
            'Characteristic': 'Right eye (OD), n (%)',
            'EYLEA (n=86)': f"{eylea_od} ({eylea_od/len(eylea_group)*100:.1f}%)",
            'FARICIMAB (n=86)': f"{faricimab_od} ({faricimab_od/len(faricimab_group)*100:.1f}%)",
            'P-value': f"{p_val:.3f}"
        })

    # 4. MNV类型分布
    if 'MNV Type' in df_analysis.columns:
        for mnv_type in sorted(df_analysis['MNV Type'].dropna().unique()):
            eylea_mnv = (eylea_group['MNV Type'] == mnv_type).sum()
            faricimab_mnv = (faricimab_group['MNV Type'] == mnv_type).sum()

            baseline_table.append({
                'Characteristic': f'MNV Type {int(mnv_type)}, n (%)',
                'EYLEA (n=86)': f"{eylea_mnv} ({eylea_mnv/len(eylea_group)*100:.1f}%)",
                'FARICIMAB (n=86)': f"{faricimab_mnv} ({faricimab_mnv/len(faricimab_group)*100:.1f}%)",
                'P-value': ''
            })

    # 5. 基线BCVA
    if 'BCVA (BL)' in df_analysis.columns:
        eylea_bcva = pd.to_numeric(eylea_group['BCVA (BL)'], errors='coerce').dropna()
        faricimab_bcva = pd.to_numeric(faricimab_group['BCVA (BL)'], errors='coerce').dropna()

        # t检验
        t_stat, p_val = stats.ttest_ind(eylea_bcva, faricimab_bcva)

        baseline_table.append({
            'Characteristic': 'Baseline BCVA (logMAR)',
            'EYLEA (n=86)': f"{eylea_bcva.mean():.3f} ± {eylea_bcva.std():.3f}",
            'FARICIMAB (n=86)': f"{faricimab_bcva.mean():.3f} ± {faricimab_bcva.std():.3f}",
            'P-value': f"{p_val:.3f}"
        })

    # 6. 基线形态学指标
    morphology_vars = ['IRF (BL)', 'SRF (BL)', 'SHRM (BL)', 'RPE rupture (BL)', 'Hemorrhage (BL)']

    for var in morphology_vars:
        if var in df_analysis.columns:
            eylea_positive = pd.to_numeric(eylea_group[var], errors='coerce').sum()
            faricimab_positive = pd.to_numeric(faricimab_group[var], errors='coerce').sum()

            # 卡方检验
            eylea_total = pd.to_numeric(eylea_group[var], errors='coerce').notna().sum()
            faricimab_total = pd.to_numeric(faricimab_group[var], errors='coerce').notna().sum()

            if eylea_total > 0 and faricimab_total > 0:
                contingency = [[eylea_positive, eylea_total - eylea_positive],
                              [faricimab_positive, faricimab_total - faricimab_positive]]
                chi2, p_val = stats.chi2_contingency(contingency)[:2]

                var_name = var.replace(' (BL)', '').replace('RPE rupture', 'RPE Rupture')
                baseline_table.append({
                    'Characteristic': f'{var_name}, n (%)',
                    'EYLEA (n=86)': f"{eylea_positive} ({eylea_positive/eylea_total*100:.1f}%)",
                    'FARICIMAB (n=86)': f"{faricimab_positive} ({faricimab_positive/faricimab_total*100:.1f}%)",
                    'P-value': f"{p_val:.3f}"
                })

    # 转换为DataFrame并保存
    baseline_df = pd.DataFrame(baseline_table)
    baseline_df.to_csv('Table1_Baseline_Characteristics.csv', index=False, encoding='utf-8')

    print("\nTable 1: Baseline Characteristics")
    print("=" * 80)
    for _, row in baseline_df.iterrows():
        print(f"{row['Characteristic']:<30} {row['EYLEA (n=86)']:<20} {row['FARICIMAB (n=86)']:<20} {row['P-value']}")

    return baseline_df

if __name__ == "__main__":
    # 文件路径
    filepath = "nAMD naive LP of eylea & faricimab 02-07-2025-2（lorena补充完临床资料）.csv"

    # 加载数据
    df_raw = load_and_clean_data(filepath)

    # 清洗数据
    df_clean = clean_data(df_raw)

    # 基础统计
    basic_statistics(df_clean)

    # 创建基线特征表
    baseline_table = create_baseline_table(df_clean)

    # 保存清洗后的数据
    df_clean.to_csv('namd_cleaned_data.csv', index=False, encoding='utf-8')
    print(f"\n清洗后的数据已保存为: namd_cleaned_data.csv")
