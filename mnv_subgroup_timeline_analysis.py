#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MNV Subgroup Timeline Analysis for nAMD Study
Shows morphological indicators (IRF, SRF, SHRM, Hemorrhage) trends across three time points
stratified by MNV Type (1, 2, 3) with statistical comparisons
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# Configure matplotlib for Adobe Acrobat editable fonts - FORCE SETTINGS
import matplotlib
# Force TrueType fonts for Adobe Acrobat compatibility
matplotlib.rcParams.update({
    'pdf.fonttype': 42,  # TrueType fonts (editable in Adobe)
    'ps.fonttype': 42,   # TrueType fonts for PostScript
    'font.family': 'sans-serif',
    'font.sans-serif': ['Arial', 'Helvetica', 'DejaVu Sans', 'Liberation Sans'],
    'font.size': 10,
    'axes.titlesize': 12,
    'axes.labelsize': 11,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 10,
    'axes.unicode_minus': False
})

# Set style for publication-quality plots
plt.style.use('default')
sns.set_palette("husl")

def calculate_proportion_ci(successes, total, confidence=0.95):
    """Calculate Wilson score interval for proportion confidence interval"""
    if total == 0:
        return 0, 0, 0
    
    p = successes / total
    z = stats.norm.ppf((1 + confidence) / 2)
    
    denominator = 1 + z**2 / total
    center = (p + z**2 / (2 * total)) / denominator
    margin = z * np.sqrt((p * (1 - p) + z**2 / (4 * total)) / total) / denominator
    
    lower = max(0, center - margin)
    upper = min(1, center + margin)
    
    return p, lower, upper

def calculate_chi_square_p(*groups):
    """Calculate chi-square test p-value for multiple proportions"""
    if len(groups) < 2:
        return 1.0
    
    # Create contingency table
    positive_counts = []
    negative_counts = []
    
    for pos, total in groups:
        if total == 0:
            return 1.0
        positive_counts.append(pos)
        negative_counts.append(total - pos)
    
    contingency_table = [positive_counts, negative_counts]
    
    try:
        chi2, p_value, _, _ = stats.chi2_contingency(contingency_table)
        return p_value
    except:
        return 1.0

def format_p_value(p_value):
    """Format p-value with appropriate precision"""
    if p_value < 0.001:
        return "p<0.001"
    elif p_value < 0.01:
        return f"p={p_value:.3f}"
    else:
        return f"p={p_value:.2f}"

def get_significance_symbol(p_value):
    """Convert p-value to significance symbol"""
    if p_value < 0.001:
        return "***"
    elif p_value < 0.01:
        return "**"
    elif p_value < 0.05:
        return "*"
    else:
        return "ns"

def create_mnv_subgroup_timeline_plot(df):
    """Create timeline plot showing morphological changes across three time points by MNV type"""
    
    # Define morphological indicators and time points
    morphology_indicators = ['IRF', 'SRF', 'SHRM', 'Hemorrhage']
    time_points = ['BL', 'Post-LP', 'Year 1']
    time_labels = ['Baseline', 'Post-LP', '1-Year']
    mnv_types = [1, 2, 3]
    
    # Prepare data structure
    results = {}
    
    for indicator in morphology_indicators:
        results[indicator] = {
            'MNV1': {'proportions': [], 'lower_ci': [], 'upper_ci': [], 'counts': []},
            'MNV2': {'proportions': [], 'lower_ci': [], 'upper_ci': [], 'counts': []},
            'MNV3': {'proportions': [], 'lower_ci': [], 'upper_ci': [], 'counts': []},
            'p_values': []
        }
        
        for time_point in time_points:
            col_name = f"{indicator} ({time_point})"
            
            # Store group data for chi-square test
            group_data = []
            
            # Calculate proportions for each MNV type
            for i, mnv_type in enumerate(mnv_types):
                mnv_key = f'MNV{mnv_type}'
                mnv_data = df[(df['MNV Type'] == mnv_type) & (df[col_name].notna())]
                
                total = len(mnv_data)
                positive = len(mnv_data[mnv_data[col_name] == 1])
                prop, lower, upper = calculate_proportion_ci(positive, total)
                
                results[indicator][mnv_key]['proportions'].append(prop)
                results[indicator][mnv_key]['lower_ci'].append(lower)
                results[indicator][mnv_key]['upper_ci'].append(upper)
                results[indicator][mnv_key]['counts'].append(f"{positive}/{total}")
                
                group_data.append((positive, total))
            
            # Calculate p-value for group comparison (chi-square test)
            p_value = calculate_chi_square_p(*group_data)
            results[indicator]['p_values'].append(p_value)
    
    # Create the plot
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.flatten()
    
    colors = {'MNV1': '#2E86AB', 'MNV2': '#A23B72', 'MNV3': '#F18F01'}
    
    for i, indicator in enumerate(morphology_indicators):
        ax = axes[i]
        
        # Plot lines for each MNV type
        for mnv_type in ['MNV1', 'MNV2', 'MNV3']:
            proportions = results[indicator][mnv_type]['proportions']
            lower_ci = results[indicator][mnv_type]['lower_ci']
            upper_ci = results[indicator][mnv_type]['upper_ci']
            
            # Convert to percentages
            proportions_pct = [p * 100 for p in proportions]
            lower_ci_pct = [l * 100 for l in lower_ci]
            upper_ci_pct = [u * 100 for u in upper_ci]
            
            # Plot line with error bars
            ax.errorbar(range(len(time_labels)), proportions_pct, 
                       yerr=[np.array(proportions_pct) - np.array(lower_ci_pct),
                             np.array(upper_ci_pct) - np.array(proportions_pct)],
                       marker='o', linewidth=2.5, markersize=8, capsize=5,
                       label=mnv_type, color=colors[mnv_type], capthick=2)
            
            # Add count labels
            for j, (prop, count) in enumerate(zip(proportions_pct, results[indicator][mnv_type]['counts'])):
                offset_y = 15 if mnv_type == 'MNV1' else (0 if mnv_type == 'MNV2' else -15)
                ax.annotate(count, (j, prop), textcoords="offset points", 
                           xytext=(0, offset_y), ha='center', 
                           fontsize=8, color=colors[mnv_type], weight='bold')
        
        # Add statistical significance annotations
        for j, p_value in enumerate(results[indicator]['p_values']):
            sig_symbol = get_significance_symbol(p_value)
            max_y = max([results[indicator][mnv]['upper_ci'][j] * 100 
                        for mnv in ['MNV1', 'MNV2', 'MNV3']])
            
            ax.annotate(sig_symbol, (j, max_y + 8), ha='center', va='bottom',
                       fontsize=12, weight='bold', 
                       color='red' if p_value < 0.05 else 'gray')
            ax.annotate(format_p_value(p_value), (j, max_y + 15), ha='center', va='bottom',
                       fontsize=8, color='red' if p_value < 0.05 else 'gray')
        
        # Customize subplot
        ax.set_title(f'{indicator} Prevalence Over Time by MNV Type', fontsize=14, weight='bold', pad=25)
        ax.set_xlabel('Time Point', fontsize=12, weight='bold')
        ax.set_ylabel('Prevalence (%)', fontsize=12, weight='bold')
        ax.set_xticks(range(len(time_labels)))
        ax.set_xticklabels(time_labels)
        ax.set_ylim(-5, 110)
        ax.grid(True, alpha=0.3)
        ax.legend(loc='upper right', frameon=True, fancybox=True, shadow=True)
        
        # Add sample size information
        mnv1_n = len(df[df['MNV Type'] == 1])
        mnv2_n = len(df[df['MNV Type'] == 2])
        mnv3_n = len(df[df['MNV Type'] == 3])
        ax.text(0.02, 0.98, f'MNV1: n={mnv1_n}\nMNV2: n={mnv2_n}\nMNV3: n={mnv3_n}', 
                transform=ax.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8),
                fontsize=10)
    
    plt.tight_layout()
    # Save as PNG, PDF, and SVG formats
    plt.savefig('MNV_Subgroup_Timeline_Analysis.png', dpi=300, bbox_inches='tight')
    plt.savefig('MNV_Subgroup_Timeline_Analysis.pdf', dpi=300, bbox_inches='tight')
    plt.savefig('MNV_Subgroup_Timeline_Analysis.svg', dpi=300, bbox_inches='tight')
    plt.close()
    
    return results

def print_mnv_timeline_summary(results):
    """Print summary of MNV subgroup timeline analysis results"""
    print("\n" + "="*80)
    print("MNV SUBGROUP TIMELINE ANALYSIS SUMMARY")
    print("="*80)
    
    morphology_indicators = ['IRF', 'SRF', 'SHRM', 'Hemorrhage']
    time_labels = ['Baseline', 'Post-LP', '1-Year']
    
    for indicator in morphology_indicators:
        print(f"\n{indicator} Analysis by MNV Type:")
        print("-" * 60)
        
        for i, time_label in enumerate(time_labels):
            mnv1_prop = results[indicator]['MNV1']['proportions'][i] * 100
            mnv1_count = results[indicator]['MNV1']['counts'][i]
            mnv2_prop = results[indicator]['MNV2']['proportions'][i] * 100
            mnv2_count = results[indicator]['MNV2']['counts'][i]
            mnv3_prop = results[indicator]['MNV3']['proportions'][i] * 100
            mnv3_count = results[indicator]['MNV3']['counts'][i]
            p_value = results[indicator]['p_values'][i]
            sig_symbol = get_significance_symbol(p_value)
            
            print(f"{time_label:10}: MNV1 {mnv1_prop:5.1f}% ({mnv1_count:>6}) | "
                  f"MNV2 {mnv2_prop:5.1f}% ({mnv2_count:>6}) | "
                  f"MNV3 {mnv3_prop:5.1f}% ({mnv3_count:>6}) "
                  f"[{format_p_value(p_value)}, {sig_symbol}]")

# Main execution
if __name__ == "__main__":
    # Load data
    df = pd.read_csv('nAMD naive LP of eylea & faricimab 02-07-2025-2（lorena补充完临床资料）.csv')
    
    # Clean drug names - standardize different variations
    df['Drug'] = df['Drug'].str.strip()  # Remove leading/trailing spaces
    df['Drug'] = df['Drug'].str.upper()  # Convert to uppercase
    df.loc[df['Drug'].str.contains('EYLEA', na=False), 'Drug'] = 'EYLEA'
    df.loc[df['Drug'].str.contains('FARICIMAB', na=False), 'Drug'] = 'FARICIMAB'
    
    print("MNV Type distribution:")
    print(df['MNV Type'].value_counts().sort_index())
    print()
    
    print("Creating MNV subgroup timeline analysis...")
    results = create_mnv_subgroup_timeline_plot(df)
    print_mnv_timeline_summary(results)
    
    print(f"\nMNV subgroup timeline analysis plot saved as 'MNV_Subgroup_Timeline_Analysis.png/.pdf/.svg'")
    print("\nAnalysis completed successfully!")
