#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级分析 - 形态学功能关联、综合疗效对比、亚组分析
Advanced Analysis - Morphology-Function Correlation, Comprehensive Efficacy, Subgroup Analysis

Author: AI Assistant
Date: 2025-01-27
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import scipy.stats as stats
from scipy.stats import ttest_ind, chi2_contingency, pearsonr
import warnings
warnings.filterwarnings('ignore')

# 设置图形参数
plt.rcParams['font.family'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300
plt.rcParams['font.size'] = 10

sns.set_style("whitegrid")

def load_processed_data():
    """加载处理过的数据"""
    df = pd.read_excel('nAMD naive LP of eylea & faricimab 15-07-2025（补充完1年视力）.xlsx')
    
    # 标准化药物名称
    df['Drug'] = df['Drug'].str.strip().str.upper()
    df.loc[df['Drug'].str.contains('EYLEA', na=False), 'Drug'] = 'EYLEA'
    df.loc[df['Drug'].str.contains('FARICIMAB', na=False), 'Drug'] = 'FARICIMAB'
    
    # 转换数据类型
    bcva_cols = ['BCVA (BL)', 'BCVA (Post-LP)', 'BCVA (Year 1)']
    for col in bcva_cols:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 计算变化量
    df['BCVA_Change_PostLP'] = df['BCVA (BL)'] - df['BCVA (Post-LP)']
    df['BCVA_Change_Year1'] = df['BCVA (BL)'] - df['BCVA (Year 1)']
    df['BCVA_Change_LP_to_Y1'] = df['BCVA (Post-LP)'] - df['BCVA (Year 1)']
    
    # 改善分类
    df['BCVA_Improved_PostLP'] = df['BCVA_Change_PostLP'] >= 0.3
    df['BCVA_Improved_Year1'] = df['BCVA_Change_Year1'] >= 0.3
    df['BCVA_Stable_Year1'] = np.abs(df['BCVA_Change_Year1']) < 0.3
    df['BCVA_Worsened_Year1'] = df['BCVA_Change_Year1'] <= -0.3
    
    # 总积液计算
    df['Total_Fluid_BL'] = (df['IRF (BL)'] == 1) | (df['SRF (BL)'] == 1)
    df['Total_Fluid_PostLP'] = (df['IRF (Post-LP)'] == 1) | (df['SRF (Post-LP)'] == 1)
    df['Total_Fluid_Y1'] = (df['IRF (Year 1)'] == 1) | (df['SRF (Year 1)'] == 1)
    
    # 干性视网膜
    df['Dry_Retina_PostLP'] = ~df['Total_Fluid_PostLP']
    df['Dry_Retina_Y1'] = ~df['Total_Fluid_Y1']
    
    # 只保留有完整1年随访的数据
    df_complete = df[df['Follow-up > 1 Year?'] == 1].copy()
    
    return df_complete

def create_morphology_function_correlation_plot(df_complete):
    """创建形态学与功能关联图"""
    print("创建形态学与功能关联图...")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 子图1: 干性视网膜达成率 vs BCVA改善
    ax = axes[0, 0]
    
    correlation_data = []
    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df_complete[df_complete['Drug'] == drug]
        if len(drug_data) == 0:
            continue
            
        dry_rate = drug_data['Dry_Retina_Y1'].mean() * 100
        bcva_improved_rate = drug_data['BCVA_Improved_Year1'].mean() * 100
        
        correlation_data.append({
            'Drug': drug,
            'Dry_Retina_Rate': dry_rate,
            'BCVA_Improved_Rate': bcva_improved_rate,
            'N': len(drug_data)
        })
    
    if correlation_data:
        corr_df = pd.DataFrame(correlation_data)
        colors = ['#2E86AB', '#A23B72']
        
        for i, (_, row) in enumerate(corr_df.iterrows()):
            ax.scatter(row['Dry_Retina_Rate'], row['BCVA_Improved_Rate'], 
                      s=row['N']*3, c=colors[i], alpha=0.7, 
                      label=f"{row['Drug']} (n={row['N']})")
            ax.annotate(row['Drug'], 
                       (row['Dry_Retina_Rate'], row['BCVA_Improved_Rate']),
                       xytext=(5, 5), textcoords='offset points')
    
    ax.set_xlabel('Dry Retina Achievement Rate (%)')
    ax.set_ylabel('BCVA Improvement Rate (%)')
    ax.set_title('Dry Retina vs BCVA Improvement\n(Year 1)', fontweight='bold')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 子图2: 各形态学指标改善率对比
    ax = axes[0, 1]
    
    morphology_data = []
    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df_complete[df_complete['Drug'] == drug]
        if len(drug_data) == 0:
            continue
            
        irf_improved = ((drug_data['IRF (BL)'] == 1) & (drug_data['IRF (Year 1)'] == 0)).mean() * 100
        srf_improved = ((drug_data['SRF (BL)'] == 1) & (drug_data['SRF (Year 1)'] == 0)).mean() * 100
        shrm_improved = ((drug_data['SHRM (BL)'] == 1) & (drug_data['SHRM (Year 1)'] == 0)).mean() * 100
        hemorrhage_improved = ((drug_data['Hemorrhage (BL)'] == 1) & (drug_data['Hemorrhage (Year 1)'] == 0)).mean() * 100
        
        morphology_data.append({
            'Drug': drug,
            'IRF': irf_improved,
            'SRF': srf_improved,
            'SHRM': shrm_improved,
            'Hemorrhage': hemorrhage_improved
        })
    
    if morphology_data:
        morph_df = pd.DataFrame(morphology_data)
        x = np.arange(len(morph_df))
        width = 0.2
        
        indicators = ['IRF', 'SRF', 'SHRM', 'Hemorrhage']
        colors = ['lightblue', 'lightgreen', 'orange', 'lightcoral']
        
        for i, indicator in enumerate(indicators):
            ax.bar(x + i*width, morph_df[indicator], width, 
                   label=indicator, color=colors[i], alpha=0.8)
        
        ax.set_xlabel('Drug Group')
        ax.set_ylabel('Improvement Rate (%)')
        ax.set_title('Morphological Indicators Improvement\n(Baseline → Year 1)', fontweight='bold')
        ax.set_xticks(x + width * 1.5)
        ax.set_xticklabels(morph_df['Drug'])
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    # 子图3: 总积液控制与BCVA关联
    ax = axes[1, 0]
    
    # 按总积液控制情况分组分析BCVA改善
    fluid_bcva_data = []
    
    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df_complete[df_complete['Drug'] == drug]
        if len(drug_data) == 0:
            continue
            
        # 有总积液的患者
        with_fluid = drug_data[drug_data['Total_Fluid_Y1'] == True]
        # 无总积液的患者
        without_fluid = drug_data[drug_data['Total_Fluid_Y1'] == False]
        
        if len(with_fluid) > 0:
            bcva_change_with = with_fluid['BCVA_Change_Year1'].mean()
            fluid_bcva_data.append({
                'Drug': drug,
                'Fluid_Status': 'With Fluid',
                'BCVA_Change': bcva_change_with,
                'N': len(with_fluid)
            })
        
        if len(without_fluid) > 0:
            bcva_change_without = without_fluid['BCVA_Change_Year1'].mean()
            fluid_bcva_data.append({
                'Drug': drug,
                'Fluid_Status': 'Dry Retina',
                'BCVA_Change': bcva_change_without,
                'N': len(without_fluid)
            })
    
    if fluid_bcva_data:
        fluid_df = pd.DataFrame(fluid_bcva_data)
        
        # 创建分组条形图
        drugs = fluid_df['Drug'].unique()
        x = np.arange(len(drugs))
        width = 0.35
        
        with_fluid_data = fluid_df[fluid_df['Fluid_Status'] == 'With Fluid']
        dry_retina_data = fluid_df[fluid_df['Fluid_Status'] == 'Dry Retina']
        
        if len(with_fluid_data) > 0:
            ax.bar(x - width/2, with_fluid_data['BCVA_Change'], width, 
                   label='With Fluid', color='lightcoral', alpha=0.8)
        
        if len(dry_retina_data) > 0:
            ax.bar(x + width/2, dry_retina_data['BCVA_Change'], width, 
                   label='Dry Retina', color='lightgreen', alpha=0.8)
        
        ax.set_xlabel('Drug Group')
        ax.set_ylabel('Mean BCVA Change (logMAR)')
        ax.set_title('BCVA Change by Fluid Status\n(Year 1)', fontweight='bold')
        ax.set_xticks(x)
        ax.set_xticklabels(drugs)
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    
    # 子图4: 相关性热图
    ax = axes[1, 1]
    
    # 计算相关性矩阵
    correlation_vars = ['BCVA_Change_Year1', 'Dry_Retina_Y1', 'BCVA_Improved_Year1']
    
    # 为每个药物组计算相关性
    corr_data = []
    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df_complete[df_complete['Drug'] == drug]
        if len(drug_data) < 10:  # 样本量太小跳过
            continue
            
        # 计算数值变量
        drug_data_numeric = drug_data.copy()
        drug_data_numeric['Dry_Retina_Y1'] = drug_data_numeric['Dry_Retina_Y1'].astype(int)
        drug_data_numeric['BCVA_Improved_Year1'] = drug_data_numeric['BCVA_Improved_Year1'].astype(int)
        
        corr_matrix = drug_data_numeric[correlation_vars].corr()
        
        # 显示相关性热图
        if drug == 'EYLEA':
            sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0,
                       ax=ax, cbar_kws={'label': 'Correlation Coefficient'})
            ax.set_title(f'Correlation Matrix\n(Combined Data)', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('Figure4_Morphology_Function_Correlation.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Figure4_Morphology_Function_Correlation.png 已保存")

def create_comprehensive_efficacy_comparison(df_complete):
    """创建综合疗效对比图"""
    print("创建综合疗效对比图...")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 子图1: 多维度疗效雷达图
    ax = axes[0, 0]
    
    # 计算各维度指标
    radar_data = []
    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df_complete[df_complete['Drug'] == drug]
        if len(drug_data) == 0:
            continue
            
        bcva_improved = drug_data['BCVA_Improved_Year1'].mean() * 100
        dry_retina = drug_data['Dry_Retina_Y1'].mean() * 100
        irf_improved = ((drug_data['IRF (BL)'] == 1) & (drug_data['IRF (Year 1)'] == 0)).mean() * 100
        srf_improved = ((drug_data['SRF (BL)'] == 1) & (drug_data['SRF (Year 1)'] == 0)).mean() * 100
        shrm_improved = ((drug_data['SHRM (BL)'] == 1) & (drug_data['SHRM (Year 1)'] == 0)).mean() * 100
        
        radar_data.append({
            'Drug': drug,
            'BCVA_Improved': bcva_improved,
            'Dry_Retina': dry_retina,
            'IRF_Improved': irf_improved,
            'SRF_Improved': srf_improved,
            'SHRM_Improved': shrm_improved
        })
    
    if radar_data:
        # 简化的条形图代替雷达图
        radar_df = pd.DataFrame(radar_data)
        categories = ['BCVA_Improved', 'Dry_Retina', 'IRF_Improved', 'SRF_Improved', 'SHRM_Improved']
        
        x = np.arange(len(categories))
        width = 0.35
        
        if len(radar_df) >= 2:
            ax.bar(x - width/2, radar_df.iloc[0][categories], width, 
                   label=radar_df.iloc[0]['Drug'], color='#2E86AB', alpha=0.8)
            ax.bar(x + width/2, radar_df.iloc[1][categories], width, 
                   label=radar_df.iloc[1]['Drug'], color='#A23B72', alpha=0.8)
        
        ax.set_xlabel('Efficacy Dimensions')
        ax.set_ylabel('Success Rate (%)')
        ax.set_title('Multi-dimensional Efficacy Comparison\n(Year 1)', fontweight='bold')
        ax.set_xticks(x)
        ax.set_xticklabels(['BCVA\nImproved', 'Dry\nRetina', 'IRF\nImproved', 
                           'SRF\nImproved', 'SHRM\nImproved'], rotation=45)
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    # 子图2: 疗效维持时间分析
    ax = axes[0, 1]
    
    # 分析从负荷期后到1年的变化
    maintenance_data = []
    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df_complete[df_complete['Drug'] == drug]
        if len(drug_data) == 0:
            continue
            
        # 负荷期后改善的患者
        improved_postlp = drug_data[drug_data['BCVA_Improved_PostLP'] == True]
        
        if len(improved_postlp) > 0:
            # 在1年时仍然改善的比例
            still_improved = (improved_postlp['BCVA_Improved_Year1'] == True).mean() * 100
            # 在1年时稳定的比例
            became_stable = (improved_postlp['BCVA_Stable_Year1'] == True).mean() * 100
            # 在1年时恶化的比例
            became_worse = (improved_postlp['BCVA_Worsened_Year1'] == True).mean() * 100
            
            maintenance_data.append({
                'Drug': drug,
                'Still_Improved': still_improved,
                'Became_Stable': became_stable,
                'Became_Worse': became_worse,
                'N_Improved_PostLP': len(improved_postlp)
            })
    
    if maintenance_data:
        maint_df = pd.DataFrame(maintenance_data)
        
        # 堆叠条形图
        bottom_stable = maint_df['Still_Improved']
        bottom_worse = bottom_stable + maint_df['Became_Stable']
        
        ax.bar(maint_df['Drug'], maint_df['Still_Improved'], 
               label='Still Improved', color='darkgreen', alpha=0.8)
        ax.bar(maint_df['Drug'], maint_df['Became_Stable'], 
               bottom=bottom_stable, label='Became Stable', color='yellow', alpha=0.8)
        ax.bar(maint_df['Drug'], maint_df['Became_Worse'], 
               bottom=bottom_worse, label='Became Worse', color='red', alpha=0.8)
        
        ax.set_ylabel('Percentage (%)')
        ax.set_title('Efficacy Maintenance Analysis\n(Post-LP Improved Patients)', fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 添加样本量注释
        for i, (_, row) in enumerate(maint_df.iterrows()):
            ax.text(i, 105, f"n={row['N_Improved_PostLP']}", ha='center', va='bottom')
    
    # 子图3和4: 预留给其他分析
    ax = axes[1, 0]
    ax.text(0.5, 0.5, 'Additional Analysis\n(Reserved)', ha='center', va='center', 
            transform=ax.transAxes, fontsize=14, style='italic')
    ax.set_title('Reserved for Future Analysis', fontweight='bold')
    
    ax = axes[1, 1]
    ax.text(0.5, 0.5, 'Additional Analysis\n(Reserved)', ha='center', va='center', 
            transform=ax.transAxes, fontsize=14, style='italic')
    ax.set_title('Reserved for Future Analysis', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('Figure5_Comprehensive_Efficacy_Comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Figure5_Comprehensive_Efficacy_Comparison.png 已保存")

if __name__ == "__main__":
    # 加载数据
    df_complete = load_processed_data()
    
    print(f"完整1年随访数据: {len(df_complete)}例")
    print(f"药物分布: {df_complete['Drug'].value_counts().to_dict()}")
    
    # 创建形态学功能关联图
    create_morphology_function_correlation_plot(df_complete)
    
    # 创建综合疗效对比图
    create_comprehensive_efficacy_comparison(df_complete)
    
    print("\n=== 高级分析完成 ===")
    print("已生成文件:")
    print("- Figure4_Morphology_Function_Correlation.png")
    print("- Figure5_Comprehensive_Efficacy_Comparison.png")
