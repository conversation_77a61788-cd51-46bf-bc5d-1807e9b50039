#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
亚组分析和汇总表格生成
Subgroup Analysis and Summary Tables Generation

Author: AI Assistant
Date: 2025-01-27
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import scipy.stats as stats
from scipy.stats import ttest_ind, chi2_contingency
import warnings
warnings.filterwarnings('ignore')

# 设置图形参数
plt.rcParams['font.family'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300
plt.rcParams['font.size'] = 10

sns.set_style("whitegrid")

def load_processed_data():
    """加载处理过的数据"""
    df = pd.read_excel('nAMD naive LP of eylea & faricimab 15-07-2025（补充完1年视力）.xlsx')
    
    # 标准化药物名称
    df['Drug'] = df['Drug'].str.strip().str.upper()
    df.loc[df['Drug'].str.contains('EYLEA', na=False), 'Drug'] = 'EYLEA'
    df.loc[df['Drug'].str.contains('FARICIMAB', na=False), 'Drug'] = 'FARICIMAB'
    
    # 转换数据类型
    bcva_cols = ['BCVA (BL)', 'BCVA (Post-LP)', 'BCVA (Year 1)']
    for col in bcva_cols:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 计算变化量
    df['BCVA_Change_PostLP'] = df['BCVA (BL)'] - df['BCVA (Post-LP)']
    df['BCVA_Change_Year1'] = df['BCVA (BL)'] - df['BCVA (Year 1)']
    df['BCVA_Change_LP_to_Y1'] = df['BCVA (Post-LP)'] - df['BCVA (Year 1)']
    
    # 改善分类
    df['BCVA_Improved_PostLP'] = df['BCVA_Change_PostLP'] >= 0.3
    df['BCVA_Improved_Year1'] = df['BCVA_Change_Year1'] >= 0.3
    df['BCVA_Stable_Year1'] = np.abs(df['BCVA_Change_Year1']) < 0.3
    df['BCVA_Worsened_Year1'] = df['BCVA_Change_Year1'] <= -0.3
    
    # 总积液计算
    df['Total_Fluid_BL'] = (df['IRF (BL)'] == 1) | (df['SRF (BL)'] == 1)
    df['Total_Fluid_PostLP'] = (df['IRF (Post-LP)'] == 1) | (df['SRF (Post-LP)'] == 1)
    df['Total_Fluid_Y1'] = (df['IRF (Year 1)'] == 1) | (df['SRF (Year 1)'] == 1)
    
    # 干性视网膜
    df['Dry_Retina_PostLP'] = ~df['Total_Fluid_PostLP']
    df['Dry_Retina_Y1'] = ~df['Total_Fluid_Y1']
    
    # 基线BCVA分层
    df['BCVA_BL_Category'] = pd.cut(df['BCVA (BL)'], 
                                   bins=[0, 0.5, 1.0, float('inf')], 
                                   labels=['Good (≤0.5)', 'Moderate (0.5-1.0)', 'Poor (>1.0)'])
    
    # 只保留有完整1年随访的数据
    df_complete = df[df['Follow-up > 1 Year?'] == 1].copy()
    
    return df_complete

def create_subgroup_analysis_plot(df_complete):
    """创建亚组分析图"""
    print("创建亚组分析图...")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 子图1: 按基线BCVA分层的疗效分析
    ax = axes[0, 0]
    
    subgroup_data = []
    for drug in ['EYLEA', 'FARICIMAB']:
        for category in ['Good (≤0.5)', 'Moderate (0.5-1.0)', 'Poor (>1.0)']:
            subgroup = df_complete[(df_complete['Drug'] == drug) & 
                                 (df_complete['BCVA_BL_Category'] == category)]
            
            if len(subgroup) > 0:
                bcva_change_mean = subgroup['BCVA_Change_Year1'].mean()
                bcva_change_std = subgroup['BCVA_Change_Year1'].std()
                improved_rate = subgroup['BCVA_Improved_Year1'].mean() * 100
                
                subgroup_data.append({
                    'Drug': drug,
                    'BCVA_Category': category,
                    'N': len(subgroup),
                    'BCVA_Change_Mean': bcva_change_mean,
                    'BCVA_Change_Std': bcva_change_std,
                    'Improved_Rate': improved_rate
                })
    
    if subgroup_data:
        subgroup_df = pd.DataFrame(subgroup_data)
        
        # 创建分组条形图
        categories = subgroup_df['BCVA_Category'].unique()
        x = np.arange(len(categories))
        width = 0.35
        
        eylea_data = subgroup_df[subgroup_df['Drug'] == 'EYLEA']
        faricimab_data = subgroup_df[subgroup_df['Drug'] == 'FARICIMAB']
        
        if len(eylea_data) > 0:
            ax.bar(x - width/2, eylea_data['BCVA_Change_Mean'], width, 
                   yerr=eylea_data['BCVA_Change_Std'], 
                   label='EYLEA', color='#2E86AB', alpha=0.8, capsize=5)
        
        if len(faricimab_data) > 0:
            ax.bar(x + width/2, faricimab_data['BCVA_Change_Mean'], width, 
                   yerr=faricimab_data['BCVA_Change_Std'],
                   label='FARICIMAB', color='#A23B72', alpha=0.8, capsize=5)
        
        ax.set_xlabel('Baseline BCVA Category')
        ax.set_ylabel('Mean BCVA Change (logMAR)')
        ax.set_title('BCVA Change by Baseline Vision\n(Year 1)', fontweight='bold')
        ax.set_xticks(x)
        ax.set_xticklabels(categories, rotation=45)
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.axhline(y=0, color='red', linestyle='--', alpha=0.7)
        
        # 添加样本量
        for i, category in enumerate(categories):
            eylea_n = eylea_data[eylea_data['BCVA_Category'] == category]['N'].values
            faricimab_n = faricimab_data[faricimab_data['BCVA_Category'] == category]['N'].values
            
            if len(eylea_n) > 0:
                ax.text(i - width/2, -0.8, f'n={eylea_n[0]}', ha='center', va='top', fontsize=8)
            if len(faricimab_n) > 0:
                ax.text(i + width/2, -0.8, f'n={faricimab_n[0]}', ha='center', va='top', fontsize=8)
    
    # 子图2: 按MNV类型分层的长期疗效
    ax = axes[0, 1]
    
    mnv_data = []
    for drug in ['EYLEA', 'FARICIMAB']:
        for mnv_type in [1, 2, 3]:
            mnv_subgroup = df_complete[(df_complete['Drug'] == drug) & 
                                     (df_complete['MNV Type'] == mnv_type)]
            
            if len(mnv_subgroup) > 0:
                bcva_change_mean = mnv_subgroup['BCVA_Change_Year1'].mean()
                dry_retina_rate = mnv_subgroup['Dry_Retina_Y1'].mean() * 100
                
                mnv_data.append({
                    'Drug': drug,
                    'MNV_Type': f'Type {mnv_type}',
                    'N': len(mnv_subgroup),
                    'BCVA_Change_Mean': bcva_change_mean,
                    'Dry_Retina_Rate': dry_retina_rate
                })
    
    if mnv_data:
        mnv_df = pd.DataFrame(mnv_data)
        
        # 散点图显示BCVA变化 vs 干性视网膜率
        colors = {'EYLEA': '#2E86AB', 'FARICIMAB': '#A23B72'}
        markers = {'Type 1': 'o', 'Type 2': 's', 'Type 3': '^'}
        
        for _, row in mnv_df.iterrows():
            ax.scatter(row['Dry_Retina_Rate'], row['BCVA_Change_Mean'], 
                      s=row['N']*5, c=colors[row['Drug']], 
                      marker=markers[row['MNV_Type']], alpha=0.7,
                      label=f"{row['Drug']} {row['MNV_Type']}")
            
            # 添加标注
            ax.annotate(f"{row['Drug']}\n{row['MNV_Type']}\n(n={row['N']})", 
                       (row['Dry_Retina_Rate'], row['BCVA_Change_Mean']),
                       xytext=(5, 5), textcoords='offset points', fontsize=8)
        
        ax.set_xlabel('Dry Retina Achievement Rate (%)')
        ax.set_ylabel('Mean BCVA Change (logMAR)')
        ax.set_title('Efficacy by MNV Type\n(Year 1)', fontweight='bold')
        ax.grid(True, alpha=0.3)
        ax.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    
    # 子图3: 治疗反应分层分析
    ax = axes[1, 0]
    
    # 按负荷期后反应分层
    response_data = []
    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df_complete[df_complete['Drug'] == drug]
        
        # 负荷期后改善的患者
        responders = drug_data[drug_data['BCVA_Improved_PostLP'] == True]
        # 负荷期后未改善的患者
        non_responders = drug_data[drug_data['BCVA_Improved_PostLP'] == False]
        
        if len(responders) > 0:
            y1_improved_rate = responders['BCVA_Improved_Year1'].mean() * 100
            response_data.append({
                'Drug': drug,
                'Response_Group': 'Post-LP Responders',
                'N': len(responders),
                'Y1_Improved_Rate': y1_improved_rate
            })
        
        if len(non_responders) > 0:
            y1_improved_rate = non_responders['BCVA_Improved_Year1'].mean() * 100
            response_data.append({
                'Drug': drug,
                'Response_Group': 'Post-LP Non-responders',
                'N': len(non_responders),
                'Y1_Improved_Rate': y1_improved_rate
            })
    
    if response_data:
        response_df = pd.DataFrame(response_data)
        
        # 分组条形图
        groups = response_df['Response_Group'].unique()
        x = np.arange(len(groups))
        width = 0.35
        
        eylea_resp = response_df[response_df['Drug'] == 'EYLEA']
        faricimab_resp = response_df[response_df['Drug'] == 'FARICIMAB']
        
        if len(eylea_resp) > 0:
            ax.bar(x - width/2, eylea_resp['Y1_Improved_Rate'], width, 
                   label='EYLEA', color='#2E86AB', alpha=0.8)
        
        if len(faricimab_resp) > 0:
            ax.bar(x + width/2, faricimab_resp['Y1_Improved_Rate'], width, 
                   label='FARICIMAB', color='#A23B72', alpha=0.8)
        
        ax.set_xlabel('Post-LP Response Group')
        ax.set_ylabel('Year 1 Improvement Rate (%)')
        ax.set_title('Long-term Efficacy by Initial Response\n(Year 1)', fontweight='bold')
        ax.set_xticks(x)
        ax.set_xticklabels(['Responders', 'Non-responders'])
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    # 子图4: 安全性分析（如果有相关数据）
    ax = axes[1, 1]
    
    # 简单的安全性指标分析
    safety_data = []
    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df_complete[df_complete['Drug'] == drug]
        
        if len(drug_data) > 0:
            # 新发RPE破裂率
            new_rpe_rupture = ((drug_data['RPE rupture (BL)'] == 0) & 
                              (drug_data['RPE rupture (Year 1)'] == 1)).mean() * 100
            
            # 新发出血率
            new_hemorrhage = ((drug_data['Hemorrhage (BL)'] == 0) & 
                             (drug_data['Hemorrhage (Year 1)'] == 1)).mean() * 100
            
            safety_data.append({
                'Drug': drug,
                'N': len(drug_data),
                'New_RPE_Rupture_Rate': new_rpe_rupture,
                'New_Hemorrhage_Rate': new_hemorrhage
            })
    
    if safety_data:
        safety_df = pd.DataFrame(safety_data)
        
        x = np.arange(len(safety_df))
        width = 0.35
        
        ax.bar(x - width/2, safety_df['New_RPE_Rupture_Rate'], width, 
               label='New RPE Rupture', color='orange', alpha=0.8)
        ax.bar(x + width/2, safety_df['New_Hemorrhage_Rate'], width, 
               label='New Hemorrhage', color='red', alpha=0.8)
        
        ax.set_xlabel('Drug Group')
        ax.set_ylabel('New Event Rate (%)')
        ax.set_title('Safety Profile Analysis\n(Year 1)', fontweight='bold')
        ax.set_xticks(x)
        ax.set_xticklabels(safety_df['Drug'])
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 添加样本量
        for i, (_, row) in enumerate(safety_df.iterrows()):
            ax.text(i, max(row['New_RPE_Rupture_Rate'], row['New_Hemorrhage_Rate']) + 1, 
                   f'n={row["N"]}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('Figure6_Subgroup_Analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Figure6_Subgroup_Analysis.png 已保存")
    
    return subgroup_data, mnv_data, response_data, safety_data

def generate_comprehensive_tables(df_complete):
    """生成综合汇总表格"""
    print("生成综合汇总表格...")
    
    # 表格1: 完整基线特征表
    baseline_table = []
    
    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df_complete[df_complete['Drug'] == drug]
        
        if len(drug_data) == 0:
            continue
            
        # 计算年龄（如果有出生日期）
        if 'Date of Birth' in drug_data.columns:
            try:
                drug_data['Date of Birth'] = pd.to_datetime(drug_data['Date of Birth'], format='%d/%m/%Y', errors='coerce')
                drug_data['Age'] = (pd.Timestamp.now() - drug_data['Date of Birth']).dt.days / 365.25
                age_mean = drug_data['Age'].mean()
                age_std = drug_data['Age'].std()
            except:
                age_mean = age_std = np.nan
        else:
            age_mean = age_std = np.nan
        
        # 性别分布
        if 'Sex (0=Female, 1=Male)' in drug_data.columns:
            female_pct = (drug_data['Sex (0=Female, 1=Male)'] == 0).mean() * 100
        else:
            female_pct = np.nan
        
        # 基线BCVA
        bcva_bl_mean = drug_data['BCVA (BL)'].mean()
        bcva_bl_std = drug_data['BCVA (BL)'].std()
        
        # 基线形态学指标
        irf_bl_rate = (drug_data['IRF (BL)'] == 1).mean() * 100
        srf_bl_rate = (drug_data['SRF (BL)'] == 1).mean() * 100
        shrm_bl_rate = (drug_data['SHRM (BL)'] == 1).mean() * 100
        hemorrhage_bl_rate = (drug_data['Hemorrhage (BL)'] == 1).mean() * 100
        
        # MNV类型分布
        mnv_type_1 = (drug_data['MNV Type'] == 1).mean() * 100
        mnv_type_2 = (drug_data['MNV Type'] == 2).mean() * 100
        mnv_type_3 = (drug_data['MNV Type'] == 3).mean() * 100
        
        baseline_table.append({
            'Drug': drug,
            'N': len(drug_data),
            'Age_Mean': age_mean,
            'Age_Std': age_std,
            'Female_Percentage': female_pct,
            'BCVA_BL_Mean': bcva_bl_mean,
            'BCVA_BL_Std': bcva_bl_std,
            'IRF_BL_Rate': irf_bl_rate,
            'SRF_BL_Rate': srf_bl_rate,
            'SHRM_BL_Rate': shrm_bl_rate,
            'Hemorrhage_BL_Rate': hemorrhage_bl_rate,
            'MNV_Type1_Rate': mnv_type_1,
            'MNV_Type2_Rate': mnv_type_2,
            'MNV_Type3_Rate': mnv_type_3
        })
    
    baseline_df = pd.DataFrame(baseline_table)
    baseline_df.to_csv('Table1_Complete_Baseline_Characteristics.csv', index=False)
    
    # 表格3: 长期疗效汇总表
    efficacy_table = []
    
    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df_complete[df_complete['Drug'] == drug]
        
        if len(drug_data) == 0:
            continue
            
        # BCVA疗效指标
        bcva_change_postlp_mean = drug_data['BCVA_Change_PostLP'].mean()
        bcva_change_y1_mean = drug_data['BCVA_Change_Year1'].mean()
        
        improved_postlp_rate = drug_data['BCVA_Improved_PostLP'].mean() * 100
        improved_y1_rate = drug_data['BCVA_Improved_Year1'].mean() * 100
        stable_y1_rate = drug_data['BCVA_Stable_Year1'].mean() * 100
        worsened_y1_rate = drug_data['BCVA_Worsened_Year1'].mean() * 100
        
        # 形态学疗效指标
        dry_retina_postlp_rate = drug_data['Dry_Retina_PostLP'].mean() * 100
        dry_retina_y1_rate = drug_data['Dry_Retina_Y1'].mean() * 100
        
        irf_improved_rate = ((drug_data['IRF (BL)'] == 1) & (drug_data['IRF (Year 1)'] == 0)).mean() * 100
        srf_improved_rate = ((drug_data['SRF (BL)'] == 1) & (drug_data['SRF (Year 1)'] == 0)).mean() * 100
        shrm_improved_rate = ((drug_data['SHRM (BL)'] == 1) & (drug_data['SHRM (Year 1)'] == 0)).mean() * 100
        
        efficacy_table.append({
            'Drug': drug,
            'N': len(drug_data),
            'BCVA_Change_PostLP_Mean': bcva_change_postlp_mean,
            'BCVA_Change_Y1_Mean': bcva_change_y1_mean,
            'BCVA_Improved_PostLP_Rate': improved_postlp_rate,
            'BCVA_Improved_Y1_Rate': improved_y1_rate,
            'BCVA_Stable_Y1_Rate': stable_y1_rate,
            'BCVA_Worsened_Y1_Rate': worsened_y1_rate,
            'Dry_Retina_PostLP_Rate': dry_retina_postlp_rate,
            'Dry_Retina_Y1_Rate': dry_retina_y1_rate,
            'IRF_Improved_Rate': irf_improved_rate,
            'SRF_Improved_Rate': srf_improved_rate,
            'SHRM_Improved_Rate': shrm_improved_rate
        })
    
    efficacy_df = pd.DataFrame(efficacy_table)
    efficacy_df.to_csv('Table3_Long_term_Efficacy_Summary.csv', index=False)
    
    print("表格生成完成:")
    print("- Table1_Complete_Baseline_Characteristics.csv")
    print("- Table3_Long_term_Efficacy_Summary.csv")
    
    return baseline_df, efficacy_df

if __name__ == "__main__":
    # 加载数据
    df_complete = load_processed_data()
    
    print(f"完整1年随访数据: {len(df_complete)}例")
    print(f"药物分布: {df_complete['Drug'].value_counts().to_dict()}")
    
    # 创建亚组分析图
    subgroup_results = create_subgroup_analysis_plot(df_complete)
    
    # 生成综合表格
    baseline_df, efficacy_df = generate_comprehensive_tables(df_complete)
    
    print("\n=== 亚组分析和表格生成完成 ===")
    print("已生成文件:")
    print("- Figure6_Subgroup_Analysis.png")
    print("- Table1_Complete_Baseline_Characteristics.csv")
    print("- Table3_Long_term_Efficacy_Summary.csv")
