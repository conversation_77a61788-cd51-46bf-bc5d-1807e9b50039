#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的nAMD疗效分析 - 包含1年VA数据
Complete nAMD Efficacy Analysis with 1-Year VA Data

Author: AI Assistant
Date: 2025-01-27
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import scipy.stats as stats
from scipy.stats import ttest_ind, ttest_rel, chi2_contingency, pearsonr
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和图形参数
plt.rcParams['font.family'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300
plt.rcParams['font.size'] = 10

# 设置图形样式
sns.set_style("whitegrid")
sns.set_palette("Set2")

def load_and_clean_data():
    """加载并清洗新的Excel数据"""
    print("=== 加载和清洗数据 ===")
    
    # 读取Excel文件
    df = pd.read_excel('nAMD naive LP of eylea & faricimab 15-07-2025（补充完1年视力）.xlsx')
    
    print(f"原始数据形状: {df.shape}")
    
    # 标准化药物名称
    df['Drug'] = df['Drug'].str.strip().str.upper()
    df.loc[df['Drug'].str.contains('EYLEA', na=False), 'Drug'] = 'EYLEA'
    df.loc[df['Drug'].str.contains('FARICIMAB', na=False), 'Drug'] = 'FARICIMAB'
    
    print(f"药物分布:")
    print(df['Drug'].value_counts())
    
    # 转换BCVA数据为数值型
    bcva_cols = ['BCVA (BL)', 'BCVA (Post-LP)', 'BCVA (Year 1)']
    for col in bcva_cols:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 计算BCVA变化量
    df['BCVA_Change_PostLP'] = df['BCVA (BL)'] - df['BCVA (Post-LP)']  # 正值表示改善
    df['BCVA_Change_Year1'] = df['BCVA (BL)'] - df['BCVA (Year 1)']   # 正值表示改善
    df['BCVA_Change_LP_to_Y1'] = df['BCVA (Post-LP)'] - df['BCVA (Year 1)']  # 负荷期后到1年的变化
    
    # 创建改善分类
    df['BCVA_Improved_PostLP'] = df['BCVA_Change_PostLP'] >= 0.3
    df['BCVA_Improved_Year1'] = df['BCVA_Change_Year1'] >= 0.3
    df['BCVA_Stable_Year1'] = np.abs(df['BCVA_Change_Year1']) < 0.3
    df['BCVA_Worsened_Year1'] = df['BCVA_Change_Year1'] <= -0.3
    
    # 维持率分析（负荷期后改善的患者在1年时是否维持）
    df['BCVA_Maintained'] = (df['BCVA_Improved_PostLP']) & (df['BCVA_Change_LP_to_Y1'] >= -0.3)
    
    print(f"清洗后数据形状: {df.shape}")
    print(f"有1年随访数据的患者: {df['Follow-up > 1 Year?'].sum()}例")
    
    return df

def analyze_complete_bcva_efficacy(df):
    """完整的BCVA疗效分析"""
    print("\n=== 完整BCVA疗效分析 ===")
    
    # 只分析有完整1年随访数据的患者
    df_complete = df[df['Follow-up > 1 Year?'] == 1].copy()
    
    results = []
    
    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df_complete[df_complete['Drug'] == drug]
        n = len(drug_data)
        
        if n == 0:
            continue
            
        # 基线BCVA
        bcva_bl_mean = drug_data['BCVA (BL)'].mean()
        bcva_bl_std = drug_data['BCVA (BL)'].std()
        
        # 负荷期后BCVA
        bcva_postlp_mean = drug_data['BCVA (Post-LP)'].mean()
        bcva_postlp_std = drug_data['BCVA (Post-LP)'].std()
        
        # 1年BCVA
        bcva_y1_mean = drug_data['BCVA (Year 1)'].mean()
        bcva_y1_std = drug_data['BCVA (Year 1)'].std()
        
        # BCVA变化量
        change_postlp_mean = drug_data['BCVA_Change_PostLP'].mean()
        change_postlp_std = drug_data['BCVA_Change_PostLP'].std()
        
        change_y1_mean = drug_data['BCVA_Change_Year1'].mean()
        change_y1_std = drug_data['BCVA_Change_Year1'].std()
        
        # 改善率
        improved_postlp_rate = drug_data['BCVA_Improved_PostLP'].mean() * 100
        improved_y1_rate = drug_data['BCVA_Improved_Year1'].mean() * 100
        stable_y1_rate = drug_data['BCVA_Stable_Year1'].mean() * 100
        worsened_y1_rate = drug_data['BCVA_Worsened_Year1'].mean() * 100
        
        # 维持率
        maintained_rate = drug_data['BCVA_Maintained'].mean() * 100
        
        results.append({
            'Drug': drug,
            'N_Complete': n,
            'BCVA_BL_Mean': bcva_bl_mean,
            'BCVA_BL_Std': bcva_bl_std,
            'BCVA_PostLP_Mean': bcva_postlp_mean,
            'BCVA_PostLP_Std': bcva_postlp_std,
            'BCVA_Y1_Mean': bcva_y1_mean,
            'BCVA_Y1_Std': bcva_y1_std,
            'Change_PostLP_Mean': change_postlp_mean,
            'Change_PostLP_Std': change_postlp_std,
            'Change_Y1_Mean': change_y1_mean,
            'Change_Y1_Std': change_y1_std,
            'Improved_PostLP_Rate': improved_postlp_rate,
            'Improved_Y1_Rate': improved_y1_rate,
            'Stable_Y1_Rate': stable_y1_rate,
            'Worsened_Y1_Rate': worsened_y1_rate,
            'Maintained_Rate': maintained_rate
        })
    
    results_df = pd.DataFrame(results)
    
    # 统计学比较
    eylea_data = df_complete[df_complete['Drug'] == 'EYLEA']
    faricimab_data = df_complete[df_complete['Drug'] == 'FARICIMAB']
    
    # t检验
    comparisons = {}
    if len(eylea_data) > 0 and len(faricimab_data) > 0:
        # 基线BCVA比较
        t_stat, p_val = ttest_ind(eylea_data['BCVA (BL)'].dropna(), 
                                  faricimab_data['BCVA (BL)'].dropna())
        comparisons['BCVA_BL_pvalue'] = p_val
        
        # 1年BCVA比较
        t_stat, p_val = ttest_ind(eylea_data['BCVA (Year 1)'].dropna(), 
                                  faricimab_data['BCVA (Year 1)'].dropna())
        comparisons['BCVA_Y1_pvalue'] = p_val
        
        # BCVA变化量比较
        t_stat, p_val = ttest_ind(eylea_data['BCVA_Change_Year1'].dropna(), 
                                  faricimab_data['BCVA_Change_Year1'].dropna())
        comparisons['BCVA_Change_Y1_pvalue'] = p_val
    
    print("BCVA疗效分析完成")
    return results_df, comparisons, df_complete

def create_complete_bcva_trend_plot(df_complete, results_df, comparisons):
    """创建完整的BCVA趋势图"""
    print("创建完整BCVA趋势图...")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    
    # 子图1: BCVA趋势线图
    timepoints = ['Baseline', 'Post-LP', 'Year 1']
    
    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df_complete[df_complete['Drug'] == drug]
        if len(drug_data) == 0:
            continue
            
        means = [
            drug_data['BCVA (BL)'].mean(),
            drug_data['BCVA (Post-LP)'].mean(),
            drug_data['BCVA (Year 1)'].mean()
        ]
        
        stds = [
            drug_data['BCVA (BL)'].std(),
            drug_data['BCVA (Post-LP)'].std(),
            drug_data['BCVA (Year 1)'].std()
        ]
        
        color = '#2E86AB' if drug == 'EYLEA' else '#A23B72'
        ax1.plot(timepoints, means, 'o-', color=color, linewidth=2, 
                markersize=8, label=f'{drug} (n={len(drug_data)})')
        ax1.errorbar(timepoints, means, yerr=stds, color=color, capsize=5, alpha=0.7)
    
    ax1.set_xlabel('Timepoint', fontsize=12)
    ax1.set_ylabel('BCVA (logMAR)', fontsize=12)
    ax1.set_title('Complete BCVA Trend Analysis\n(Baseline → Post-LP → Year 1)', 
                  fontsize=14, fontweight='bold')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.invert_yaxis()  # 更好的视力在上方
    
    # 添加p值注释
    if 'BCVA_Y1_pvalue' in comparisons:
        p_val = comparisons['BCVA_Y1_pvalue']
        p_text = f'p = {p_val:.3f}' if p_val >= 0.001 else 'p < 0.001'
        ax1.text(0.5, 0.95, f'Year 1 BCVA comparison: {p_text}', 
                transform=ax1.transAxes, ha='center', va='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 子图2: BCVA变化量箱线图
    change_data = []
    change_labels = []
    colors = []
    
    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df_complete[df_complete['Drug'] == drug]
        if len(drug_data) == 0:
            continue
            
        change_data.append(drug_data['BCVA_Change_Year1'].dropna())
        change_labels.append(f'{drug}\n(n={len(drug_data)})')
        colors.append('#2E86AB' if drug == 'EYLEA' else '#A23B72')
    
    bp = ax2.boxplot(change_data, labels=change_labels, patch_artist=True)
    for patch, color in zip(bp['boxes'], colors):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)
    
    ax2.axhline(y=0, color='red', linestyle='--', alpha=0.7, linewidth=1)
    ax2.axhline(y=0.3, color='green', linestyle='--', alpha=0.7, linewidth=1)
    ax2.text(0.02, 0.3, 'Improvement threshold', transform=ax2.get_yaxis_transform(), 
             va='bottom', fontsize=9, color='green')
    
    ax2.set_ylabel('BCVA Change from Baseline (logMAR)', fontsize=12)
    ax2.set_title('Long-term BCVA Change Distribution\n(Positive = Improvement)', 
                  fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    
    # 添加统计比较
    if 'BCVA_Change_Y1_pvalue' in comparisons:
        p_val = comparisons['BCVA_Change_Y1_pvalue']
        p_text = f'p = {p_val:.3f}' if p_val >= 0.001 else 'p < 0.001'
        y_max = max([max(data) for data in change_data])
        ax2.text(1.5, y_max + 0.1, p_text, ha='center', va='bottom', fontsize=12,
                bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('Figure1_Complete_BCVA_Trend.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Figure1_Complete_BCVA_Trend.png 已保存")

def create_bcva_changes_analysis(df_complete):
    """创建BCVA变化详细分析图"""
    print("创建BCVA变化详细分析图...")

    fig, axes = plt.subplots(2, 2, figsize=(16, 12))

    # 子图1: 基线→负荷期后变化
    ax = axes[0, 0]
    change_data = []
    labels = []
    colors = []

    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df_complete[df_complete['Drug'] == drug]
        if len(drug_data) == 0:
            continue
        change_data.append(drug_data['BCVA_Change_PostLP'].dropna())
        labels.append(f'{drug}\n(n={len(drug_data)})')
        colors.append('#2E86AB' if drug == 'EYLEA' else '#A23B72')

    bp1 = ax.boxplot(change_data, labels=labels, patch_artist=True)
    for patch, color in zip(bp1['boxes'], colors):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)

    ax.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax.axhline(y=0.3, color='green', linestyle='--', alpha=0.7)
    ax.set_title('BCVA Change: Baseline → Post-LP', fontweight='bold')
    ax.set_ylabel('BCVA Change (logMAR)')
    ax.grid(True, alpha=0.3)

    # 子图2: 负荷期后→1年变化
    ax = axes[0, 1]
    change_data = []
    labels = []

    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df_complete[df_complete['Drug'] == drug]
        if len(drug_data) == 0:
            continue
        change_data.append(drug_data['BCVA_Change_LP_to_Y1'].dropna())
        labels.append(f'{drug}\n(n={len(drug_data)})')

    bp2 = ax.boxplot(change_data, labels=labels, patch_artist=True)
    for patch, color in zip(bp2['boxes'], colors):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)

    ax.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax.set_title('BCVA Change: Post-LP → Year 1', fontweight='bold')
    ax.set_ylabel('BCVA Change (logMAR)')
    ax.grid(True, alpha=0.3)

    # 子图3: 基线→1年总变化
    ax = axes[1, 0]
    change_data = []
    labels = []

    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df_complete[df_complete['Drug'] == drug]
        if len(drug_data) == 0:
            continue
        change_data.append(drug_data['BCVA_Change_Year1'].dropna())
        labels.append(f'{drug}\n(n={len(drug_data)})')

    bp3 = ax.boxplot(change_data, labels=labels, patch_artist=True)
    for patch, color in zip(bp3['boxes'], colors):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)

    ax.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax.axhline(y=0.3, color='green', linestyle='--', alpha=0.7)
    ax.set_title('Total BCVA Change: Baseline → Year 1', fontweight='bold')
    ax.set_ylabel('BCVA Change (logMAR)')
    ax.grid(True, alpha=0.3)

    # 子图4: 长期维持率分析
    ax = axes[1, 1]

    maintenance_data = []
    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df_complete[df_complete['Drug'] == drug]
        if len(drug_data) == 0:
            continue

        improved_postlp = drug_data['BCVA_Improved_PostLP'].sum()
        maintained = drug_data['BCVA_Maintained'].sum()
        improved_y1 = drug_data['BCVA_Improved_Year1'].sum()
        stable_y1 = drug_data['BCVA_Stable_Year1'].sum()
        worsened_y1 = drug_data['BCVA_Worsened_Year1'].sum()

        maintenance_data.append({
            'Drug': drug,
            'Improved_PostLP': improved_postlp / len(drug_data) * 100,
            'Maintained': maintained / len(drug_data) * 100,
            'Improved_Y1': improved_y1 / len(drug_data) * 100,
            'Stable_Y1': stable_y1 / len(drug_data) * 100,
            'Worsened_Y1': worsened_y1 / len(drug_data) * 100
        })

    if maintenance_data:
        maintenance_df = pd.DataFrame(maintenance_data)
        x = np.arange(len(maintenance_df))
        width = 0.15

        ax.bar(x - 2*width, maintenance_df['Improved_PostLP'], width,
               label='Improved Post-LP', color='lightblue', alpha=0.8)
        ax.bar(x - width, maintenance_df['Improved_Y1'], width,
               label='Improved Year 1', color='lightgreen', alpha=0.8)
        ax.bar(x, maintenance_df['Stable_Y1'], width,
               label='Stable Year 1', color='yellow', alpha=0.8)
        ax.bar(x + width, maintenance_df['Worsened_Y1'], width,
               label='Worsened Year 1', color='lightcoral', alpha=0.8)
        ax.bar(x + 2*width, maintenance_df['Maintained'], width,
               label='Maintained', color='darkgreen', alpha=0.8)

        ax.set_xlabel('Drug Group')
        ax.set_ylabel('Percentage (%)')
        ax.set_title('Long-term Efficacy Maintenance Analysis', fontweight='bold')
        ax.set_xticks(x)
        ax.set_xticklabels(maintenance_df['Drug'])
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('Figure2_BCVA_Changes_Analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("Figure2_BCVA_Changes_Analysis.png 已保存")

def analyze_morphology_function_correlation(df_complete):
    """分析形态学与功能的关联"""
    print("分析形态学与功能关联...")

    # 计算总积液率
    df_complete['Total_Fluid_BL'] = (df_complete['IRF (BL)'] == 1) | (df_complete['SRF (BL)'] == 1)
    df_complete['Total_Fluid_PostLP'] = (df_complete['IRF (Post-LP)'] == 1) | (df_complete['SRF (Post-LP)'] == 1)
    df_complete['Total_Fluid_Y1'] = (df_complete['IRF (Year 1)'] == 1) | (df_complete['SRF (Year 1)'] == 1)

    # 干性视网膜达成率
    df_complete['Dry_Retina_PostLP'] = ~df_complete['Total_Fluid_PostLP']
    df_complete['Dry_Retina_Y1'] = ~df_complete['Total_Fluid_Y1']

    correlation_results = []

    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df_complete[df_complete['Drug'] == drug]
        if len(drug_data) == 0:
            continue

        # 干性视网膜达成率
        dry_postlp_rate = drug_data['Dry_Retina_PostLP'].mean() * 100
        dry_y1_rate = drug_data['Dry_Retina_Y1'].mean() * 100

        # 各形态学指标改善率
        irf_improved = ((drug_data['IRF (BL)'] == 1) & (drug_data['IRF (Year 1)'] == 0)).mean() * 100
        srf_improved = ((drug_data['SRF (BL)'] == 1) & (drug_data['SRF (Year 1)'] == 0)).mean() * 100
        shrm_improved = ((drug_data['SHRM (BL)'] == 1) & (drug_data['SHRM (Year 1)'] == 0)).mean() * 100

        # BCVA改善率
        bcva_improved_rate = drug_data['BCVA_Improved_Year1'].mean() * 100

        correlation_results.append({
            'Drug': drug,
            'N': len(drug_data),
            'Dry_Retina_PostLP_Rate': dry_postlp_rate,
            'Dry_Retina_Y1_Rate': dry_y1_rate,
            'IRF_Improved_Rate': irf_improved,
            'SRF_Improved_Rate': srf_improved,
            'SHRM_Improved_Rate': shrm_improved,
            'BCVA_Improved_Rate': bcva_improved_rate
        })

    correlation_df = pd.DataFrame(correlation_results)
    return correlation_df

if __name__ == "__main__":
    # 主分析流程
    df = load_and_clean_data()

    # BCVA疗效分析
    results_df, comparisons, df_complete = analyze_complete_bcva_efficacy(df)

    # 创建BCVA趋势图
    create_complete_bcva_trend_plot(df_complete, results_df, comparisons)

    # 创建BCVA变化详细分析
    create_bcva_changes_analysis(df_complete)

    # 形态学功能关联分析
    correlation_df = analyze_morphology_function_correlation(df_complete)

    # 保存结果
    results_df.to_csv('Table2_Complete_BCVA_Analysis.csv', index=False)
    correlation_df.to_csv('Table4_Morphology_Function_Correlation.csv', index=False)

    print("\n=== 第一阶段分析完成 ===")
    print("已生成文件:")
    print("- Figure1_Complete_BCVA_Trend.png")
    print("- Figure2_BCVA_Changes_Analysis.png")
    print("- Table2_Complete_BCVA_Analysis.csv")
    print("- Table4_Morphology_Function_Correlation.csv")
