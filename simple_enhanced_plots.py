#!/usr/bin/env python3
"""
简化版增强可视化脚本 - 为nAMD分析添加误差棒和P值
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import chi2_contingency, fisher_exact
import warnings
warnings.filterwarnings('ignore')

# Configure matplotlib for Adobe Acrobat editable fonts - FORCE SETTINGS
import matplotlib
# Force TrueType fonts for Adobe Acrobat compatibility
matplotlib.rcParams.update({
    'pdf.fonttype': 42,  # TrueType fonts (editable in Adobe)
    'ps.fonttype': 42,   # TrueType fonts for PostScript
    'font.family': 'sans-serif',
    'font.sans-serif': ['Arial', 'Helvetica', 'DejaVu Sans', 'Liberation Sans'],
    'font.size': 10,
    'axes.titlesize': 12,
    'axes.labelsize': 11,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 10,
    'axes.unicode_minus': False
})
plt.style.use('default')
sns.set_palette("husl")

def calculate_proportion_ci(successes, total, confidence=0.95):
    """计算比例的置信区间"""
    if total == 0:
        return 0, 0, 0
    
    p = successes / total
    z = stats.norm.ppf((1 + confidence) / 2)
    
    # Wilson score interval
    denominator = 1 + z**2 / total
    centre_adjusted_probability = (p + z**2 / (2 * total)) / denominator
    adjusted_standard_deviation = np.sqrt((p * (1 - p) + z**2 / (4 * total)) / total) / denominator
    
    lower_bound = centre_adjusted_probability - z * adjusted_standard_deviation
    upper_bound = centre_adjusted_probability + z * adjusted_standard_deviation
    
    # 确保边界在0-1之间
    lower_bound = max(0, lower_bound)
    upper_bound = min(1, upper_bound)
    
    return p * 100, lower_bound * 100, upper_bound * 100

def calculate_fisher_exact_p(group1_success, group1_total, group2_success, group2_total):
    """计算Fisher精确检验的P值"""
    try:
        group1_fail = group1_total - group1_success
        group2_fail = group2_total - group2_success
        
        contingency_table = np.array([[group1_success, group1_fail],
                                    [group2_success, group2_fail]])
        
        _, p_value = fisher_exact(contingency_table)
        return p_value
    except:
        return None

def add_significance_annotation(ax, x1, x2, y, p_value, height_offset=3):
    """在图表上添加显著性标注"""
    if p_value is None:
        return
    
    # 确定显著性水平
    if p_value < 0.001:
        sig_text = '***'
    elif p_value < 0.01:
        sig_text = '**'
    elif p_value < 0.05:
        sig_text = '*'
    else:
        sig_text = 'ns'
    
    # 添加水平线
    ax.plot([x1, x2], [y, y], 'k-', linewidth=1)
    # 添加垂直线
    ax.plot([x1, x1], [y-height_offset/3, y], 'k-', linewidth=1)
    ax.plot([x2, x2], [y-height_offset/3, y], 'k-', linewidth=1)
    
    # 添加P值文本
    ax.text((x1 + x2) / 2, y + height_offset/2, f'p={p_value:.3f}\n{sig_text}', 
            ha='center', va='bottom', fontsize=8, fontweight='bold')

def create_enhanced_morphology_comparison():
    """创建增强的形态学比较图表"""
    print("正在创建增强的形态学比较图表...")
    
    # 加载数据
    df = pd.read_csv("nAMD naive LP of eylea & faricimab 02-07-2025-2（lorena补充完临床资料）.csv")
    
    # 形态学指标
    morphology_vars = ['IRF', 'SRF', 'SHRM', 'Hemorrhage']
    
    # 计算改善率和置信区间
    results = []
    
    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df[df['Drug'] == drug]
        
        for var in morphology_vars:
            baseline_col = f'{var} (BL)'
            postlp_col = f'{var} (Post-LP)'
            
            if baseline_col in df.columns and postlp_col in df.columns:
                baseline_positive = (drug_data[baseline_col] == 1).sum()
                postlp_positive = (drug_data[postlp_col] == 1).sum()
                
                # 计算改善（基线阳性 -> 负荷期后阴性）
                improved = baseline_positive - postlp_positive
                
                if baseline_positive > 0:
                    improvement_rate, ci_lower, ci_upper = calculate_proportion_ci(improved, baseline_positive)
                else:
                    improvement_rate = ci_lower = ci_upper = 0
                
                results.append({
                    'Drug': drug,
                    'Variable': var,
                    'Improvement_Rate': improvement_rate,
                    'CI_Lower': ci_lower,
                    'CI_Upper': ci_upper,
                    'Baseline_Positive': baseline_positive,
                    'Improved': improved
                })
    
    results_df = pd.DataFrame(results)
    
    # 创建图表
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))

    # 改善率比较（带误差棒和P值）
    
    x = np.arange(len(morphology_vars))
    width = 0.35
    
    eylea_data = results_df[results_df['Drug'] == 'EYLEA']
    faricimab_data = results_df[results_df['Drug'] == 'FARICIMAB']
    
    eylea_rates = []
    faricimab_rates = []
    eylea_errors = []
    faricimab_errors = []
    
    for var in morphology_vars:
        e_row = eylea_data[eylea_data['Variable'] == var]
        f_row = faricimab_data[faricimab_data['Variable'] == var]
        
        if not e_row.empty:
            eylea_rates.append(e_row['Improvement_Rate'].iloc[0])
            eylea_errors.append([e_row['Improvement_Rate'].iloc[0] - e_row['CI_Lower'].iloc[0],
                               e_row['CI_Upper'].iloc[0] - e_row['Improvement_Rate'].iloc[0]])
        else:
            eylea_rates.append(0)
            eylea_errors.append([0, 0])
            
        if not f_row.empty:
            faricimab_rates.append(f_row['Improvement_Rate'].iloc[0])
            faricimab_errors.append([f_row['Improvement_Rate'].iloc[0] - f_row['CI_Lower'].iloc[0],
                                   f_row['CI_Upper'].iloc[0] - f_row['Improvement_Rate'].iloc[0]])
        else:
            faricimab_rates.append(0)
            faricimab_errors.append([0, 0])
    
    # 转换误差格式
    eylea_errors = np.array(eylea_errors).T
    faricimab_errors = np.array(faricimab_errors).T
    
    # 确保误差值非负
    eylea_errors = np.abs(eylea_errors)
    faricimab_errors = np.abs(faricimab_errors)
    
    bars1 = ax.bar(x - width/2, eylea_rates, width, label='EYLEA', 
                   color='#2E86AB', alpha=0.8, yerr=eylea_errors, capsize=5)
    bars2 = ax.bar(x + width/2, faricimab_rates, width, label='FARICIMAB', 
                   color='#A23B72', alpha=0.8, yerr=faricimab_errors, capsize=5)
    
    ax.set_title('Improvement Rates by Morphological Indicator\n(with 95% Confidence Intervals)', 
                 fontweight='bold', fontsize=14)
    ax.set_ylabel('Improvement Rate (%)', fontsize=12)
    ax.set_xlabel('Morphological Indicator', fontsize=12)
    ax.set_xticks(x)
    ax.set_xticklabels(morphology_vars)
    ax.legend(fontsize=11)
    ax.grid(True, alpha=0.3)
    
    # 添加P值
    max_height = max(max(eylea_rates), max(faricimab_rates)) + 15
    for i, var in enumerate(morphology_vars):
        e_row = eylea_data[eylea_data['Variable'] == var]
        f_row = faricimab_data[faricimab_data['Variable'] == var]
        
        if not e_row.empty and not f_row.empty:
            p_value = calculate_fisher_exact_p(
                e_row['Improved'].iloc[0], e_row['Baseline_Positive'].iloc[0],
                f_row['Improved'].iloc[0], f_row['Baseline_Positive'].iloc[0]
            )
            if p_value is not None:
                add_significance_annotation(ax, i - width/2, i + width/2, max_height + i*8, p_value)
    
    plt.tight_layout()
    # Save as PNG, PDF, and SVG formats
    plt.savefig('Enhanced_Morphology_Comparison.png', dpi=300, bbox_inches='tight')
    plt.savefig('Enhanced_Morphology_Comparison.pdf', dpi=300, bbox_inches='tight',
                metadata={'Creator': 'matplotlib', 'Producer': 'matplotlib'})
    plt.savefig('Enhanced_Morphology_Comparison.svg', dpi=300, bbox_inches='tight')
    plt.close()
    
    return results_df

def create_enhanced_year1_comparison():
    """创建增强的1年随访比较图表"""
    print("正在创建增强的1年随访比较图表...")
    
    # 加载数据
    df = pd.read_csv("nAMD naive LP of eylea & faricimab 02-07-2025-2（lorena补充完临床资料）.csv")
    
    # 筛选有1年随访数据的患者
    year1_data = df.dropna(subset=['IRF (Year 1)', 'SRF (Year 1)', 'SHRM (Year 1)'])
    
    print(f"1年随访数据可用性:")
    for drug in ['EYLEA', 'FARICIMAB']:
        count = len(year1_data[year1_data['Drug'] == drug])
        total = len(df[df['Drug'] == drug])
        print(f"  {drug}: {count}/{total} ({count/total*100:.1f}%)")
    
    # 形态学指标
    morphology_vars = ['IRF', 'SRF', 'SHRM', 'Hemorrhage']
    
    # 计算长期改善率
    year1_results = []
    
    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = year1_data[year1_data['Drug'] == drug]
        
        for var in morphology_vars:
            baseline_col = f'{var} (BL)'
            year1_col = f'{var} (Year 1)'
            
            if baseline_col in df.columns and year1_col in df.columns:
                baseline_positive = (drug_data[baseline_col] == 1).sum()
                year1_positive = (drug_data[year1_col] == 1).sum()
                
                # 计算长期改善（基线阳性 -> 1年阴性）
                longterm_improved = baseline_positive - year1_positive
                
                if baseline_positive > 0:
                    longterm_rate, ci_lower, ci_upper = calculate_proportion_ci(longterm_improved, baseline_positive)
                else:
                    longterm_rate = ci_lower = ci_upper = 0
                
                year1_results.append({
                    'Drug': drug,
                    'Variable': var,
                    'Longterm_Improvement_Rate': longterm_rate,
                    'CI_Lower': ci_lower,
                    'CI_Upper': ci_upper,
                    'Baseline_Positive': baseline_positive,
                    'Longterm_Improved': longterm_improved
                })
    
    year1_df = pd.DataFrame(year1_results)
    
    # 创建图表
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    
    x = np.arange(len(morphology_vars))
    width = 0.35
    
    eylea_data = year1_df[year1_df['Drug'] == 'EYLEA']
    faricimab_data = year1_df[year1_df['Drug'] == 'FARICIMAB']
    
    eylea_rates = []
    faricimab_rates = []
    eylea_errors = []
    faricimab_errors = []
    
    for var in morphology_vars:
        e_row = eylea_data[eylea_data['Variable'] == var]
        f_row = faricimab_data[faricimab_data['Variable'] == var]
        
        if not e_row.empty:
            eylea_rates.append(e_row['Longterm_Improvement_Rate'].iloc[0])
            eylea_errors.append([e_row['Longterm_Improvement_Rate'].iloc[0] - e_row['CI_Lower'].iloc[0],
                               e_row['CI_Upper'].iloc[0] - e_row['Longterm_Improvement_Rate'].iloc[0]])
        else:
            eylea_rates.append(0)
            eylea_errors.append([0, 0])
            
        if not f_row.empty:
            faricimab_rates.append(f_row['Longterm_Improvement_Rate'].iloc[0])
            faricimab_errors.append([f_row['Longterm_Improvement_Rate'].iloc[0] - f_row['CI_Lower'].iloc[0],
                                   f_row['CI_Upper'].iloc[0] - f_row['Longterm_Improvement_Rate'].iloc[0]])
        else:
            faricimab_rates.append(0)
            faricimab_errors.append([0, 0])
    
    # 转换误差格式并确保非负
    eylea_errors = np.abs(np.array(eylea_errors).T)
    faricimab_errors = np.abs(np.array(faricimab_errors).T)
    
    bars1 = ax.bar(x - width/2, eylea_rates, width, label='EYLEA', 
                   color='#2E86AB', alpha=0.8, yerr=eylea_errors, capsize=5)
    bars2 = ax.bar(x + width/2, faricimab_rates, width, label='FARICIMAB', 
                   color='#A23B72', alpha=0.8, yerr=faricimab_errors, capsize=5)
    
    ax.set_title('Long-term Improvement Rates (Baseline to Year 1)\n(with 95% Confidence Intervals)', 
                 fontweight='bold', fontsize=14)
    ax.set_ylabel('Long-term Improvement Rate (%)', fontsize=12)
    ax.set_xlabel('Morphological Indicator', fontsize=12)
    ax.set_xticks(x)
    ax.set_xticklabels(morphology_vars)
    ax.legend(fontsize=11)
    ax.grid(True, alpha=0.3)
    
    # 添加P值（注意数据不平衡的限制）
    max_height = max(max(eylea_rates), max(faricimab_rates)) + 15
    for i, var in enumerate(morphology_vars):
        e_row = eylea_data[eylea_data['Variable'] == var]
        f_row = faricimab_data[faricimab_data['Variable'] == var]
        
        if not e_row.empty and not f_row.empty:
            p_value = calculate_fisher_exact_p(
                e_row['Longterm_Improved'].iloc[0], e_row['Baseline_Positive'].iloc[0],
                f_row['Longterm_Improved'].iloc[0], f_row['Baseline_Positive'].iloc[0]
            )
            if p_value is not None:
                add_significance_annotation(ax, i - width/2, i + width/2, max_height + i*8, p_value)
    
    # 添加数据不平衡警告
    ax.text(0.02, 0.98, '⚠️ 注意：1年随访数据存在组间不平衡\nEYLEA: 94.1% vs FARICIMAB: 53.6%', 
            transform=ax.transAxes, fontsize=10, verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7))
    
    plt.tight_layout()
    # Save as PNG, PDF, and SVG formats
    plt.savefig('Enhanced_Year1_Comparison.png', dpi=300, bbox_inches='tight')
    plt.savefig('Enhanced_Year1_Comparison.pdf', dpi=300, bbox_inches='tight',
                metadata={'Creator': 'matplotlib', 'Producer': 'matplotlib'})
    plt.savefig('Enhanced_Year1_Comparison.svg', dpi=300, bbox_inches='tight')
    plt.close()
    
    return year1_df

if __name__ == "__main__":
    print("=== 创建增强的nAMD形态学分析图表 ===")
    
    print("\n1. 负荷期后改善率比较")
    morphology_results = create_enhanced_morphology_comparison()
    
    print("\n2. 1年随访长期改善率比较")
    year1_results = create_enhanced_year1_comparison()
    
    print("\n✅ 增强可视化完成！")
    print("生成的文件:")
    print("- Enhanced_Morphology_Comparison.png/.pdf/.svg")
    print("- Enhanced_Year1_Comparison.png/.pdf/.svg")
    print("\n📊 所有图表现在包含:")
    print("- 95%置信区间误差棒")
    print("- 组间比较P值")
    print("- 统计显著性标注 (*, **, ***, ns)")
