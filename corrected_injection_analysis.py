#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正版：仅分析有1年随访数据的患者的Loading Phase后注射次数
Corrected Analysis: Only patients with 1-year follow-up data
"""

import pandas as pd
import numpy as np
from scipy import stats
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# Set font configuration for Adobe Acrobat compatibility
import matplotlib
matplotlib.rcParams.update({
    'pdf.fonttype': 42,  # TrueType fonts (editable in Adobe)
    'ps.fonttype': 42,   # TrueType fonts for PostScript
    'font.family': 'sans-serif',
    'font.sans-serif': ['Arial', 'Helvetica', 'DejaVu Sans', 'Liberation Sans']
})

def clean_injection_data(value):
    """清理注射次数数据"""
    if pd.isna(value):
        return np.nan
    
    # 转换为字符串并清理
    value_str = str(value).strip().lower()
    
    # 提取数字
    import re
    numbers = re.findall(r'\d+', value_str)
    
    if numbers:
        # 取第一个数字作为注射次数
        return float(numbers[0])
    else:
        return np.nan

def load_and_analyze_corrected_data():
    """加载并分析修正后的数据（仅包含1年随访患者）"""
    print("="*80)
    print("💉 修正版：仅分析有1年随访数据的患者")
    print("="*80)
    
    # 加载数据
    df = pd.read_csv("nAMD naive LP of eylea & faricimab 02-07-2025-2（lorena补充完临床资料）.csv")
    
    print(f"\n📊 原始数据统计:")
    print(f"总患者数: {len(df)}")
    print(f"EYLEA组: {len(df[df['Drug'] == 'EYLEA'])}")
    print(f"FARICIMAB组: {len(df[df['Drug'] == 'FARICIMAB'])}")
    
    # 检查随访列
    followup_col = 'Follow-up > 1 Year?'
    if followup_col not in df.columns:
        print(f"❌ 未找到列: {followup_col}")
        print("可用的列名:")
        for col in df.columns:
            if 'follow' in col.lower() or 'year' in col.lower():
                print(f"  - {col}")
        return None, None, None
    
    # 筛选有1年随访数据的患者
    print(f"\n🔍 随访数据筛选:")
    print(f"随访列名: '{followup_col}'")
    
    # 检查随访列的数据
    followup_values = df[followup_col].value_counts(dropna=False)
    print(f"随访数据分布:")
    for value, count in followup_values.items():
        print(f"  {value}: {count} 例")
    
    # 筛选有1年随访的患者 (值为1)
    df_1year = df[df[followup_col] == 1].copy()
    
    print(f"\n📋 筛选后数据统计:")
    print(f"有1年随访数据的患者: {len(df_1year)}/{len(df)} ({len(df_1year)/len(df)*100:.1f}%)")
    
    if len(df_1year) == 0:
        print("❌ 没有患者有1年随访数据")
        return None, None, None
    
    # 按药物分组
    eylea_1year = df_1year[df_1year['Drug'] == 'EYLEA']
    faricimab_1year = df_1year[df_1year['Drug'] == 'FARICIMAB']
    
    print(f"EYLEA组 (1年随访): {len(eylea_1year)}")
    print(f"FARICIMAB组 (1年随访): {len(faricimab_1year)}")
    
    # 分析Loading Phase注射次数
    print(f"\n📋 Loading Phase注射次数 (仅1年随访患者):")
    if 'LP Injections (Count)' in df.columns:
        eylea_lp = pd.to_numeric(eylea_1year['LP Injections (Count)'], errors='coerce').dropna()
        faricimab_lp = pd.to_numeric(faricimab_1year['LP Injections (Count)'], errors='coerce').dropna()
        
        if len(eylea_lp) > 0 and len(faricimab_lp) > 0:
            print(f"EYLEA组 LP注射次数: {eylea_lp.mean():.1f} ± {eylea_lp.std():.1f} (范围: {eylea_lp.min():.0f}-{eylea_lp.max():.0f})")
            print(f"FARICIMAB组 LP注射次数: {faricimab_lp.mean():.1f} ± {faricimab_lp.std():.1f} (范围: {faricimab_lp.min():.0f}-{faricimab_lp.max():.0f})")
            
            t_stat, p_val = stats.ttest_ind(eylea_lp, faricimab_lp)
            print(f"P值: {p_val:.3f}")
    
    # 分析Loading Phase后的注射次数
    print(f"\n💉 Loading Phase后注射次数 (仅1年随访患者):")
    col_name = 'Number of injection after LP '
    
    if col_name not in df.columns:
        print(f"❌ 未找到列: {col_name}")
        return None, None, None
    
    # 清理数据
    eylea_post_lp = eylea_1year[col_name].apply(clean_injection_data).dropna()
    faricimab_post_lp = faricimab_1year[col_name].apply(clean_injection_data).dropna()
    
    print(f"EYLEA组有效数据: {len(eylea_post_lp)}/{len(eylea_1year)} ({len(eylea_post_lp)/len(eylea_1year)*100:.1f}%)")
    print(f"FARICIMAB组有效数据: {len(faricimab_post_lp)}/{len(faricimab_1year)} ({len(faricimab_post_lp)/len(faricimab_1year)*100:.1f}%)")
    
    if len(eylea_post_lp) > 0 and len(faricimab_post_lp) > 0:
        print(f"\nEYLEA组 LP后注射次数: {eylea_post_lp.mean():.1f} ± {eylea_post_lp.std():.1f} (范围: {eylea_post_lp.min():.0f}-{eylea_post_lp.max():.0f})")
        print(f"FARICIMAB组 LP后注射次数: {faricimab_post_lp.mean():.1f} ± {faricimab_post_lp.std():.1f} (范围: {faricimab_post_lp.min():.0f}-{faricimab_post_lp.max():.0f})")
        
        t_stat, p_val = stats.ttest_ind(eylea_post_lp, faricimab_post_lp)
        print(f"P值: {p_val:.3f}")
        
        # 详细统计
        print(f"\n📈 详细统计:")
        print(f"EYLEA组:")
        print(f"  - 中位数: {eylea_post_lp.median():.1f}")
        print(f"  - 四分位数: Q1={eylea_post_lp.quantile(0.25):.1f}, Q3={eylea_post_lp.quantile(0.75):.1f}")
        
        print(f"FARICIMAB组:")
        print(f"  - 中位数: {faricimab_post_lp.median():.1f}")
        print(f"  - 四分位数: Q1={faricimab_post_lp.quantile(0.25):.1f}, Q3={faricimab_post_lp.quantile(0.75):.1f}")
        
        return eylea_post_lp, faricimab_post_lp, p_val
    else:
        print("❌ 没有足够的有效数据进行分析")
        return None, None, None

def analyze_injection_distribution_corrected(eylea_data, faricimab_data):
    """分析注射次数分布（修正版）"""
    print(f"\n📊 注射次数分布分析 (仅1年随访患者):")
    
    if eylea_data is not None and faricimab_data is not None:
        # 合并数据进行分布分析
        all_injections = list(range(int(min(eylea_data.min(), faricimab_data.min())), 
                                   int(max(eylea_data.max(), faricimab_data.max())) + 1))
        
        print(f"\n注射次数分布:")
        print(f"{'注射次数':<8} {'EYLEA':<20} {'FARICIMAB':<20} {'总计':<10}")
        print("-" * 60)
        
        for inj_count in all_injections:
            eylea_count = (eylea_data == inj_count).sum()
            faricimab_count = (faricimab_data == inj_count).sum()
            total_count = eylea_count + faricimab_count
            
            eylea_pct = eylea_count / len(eylea_data) * 100 if len(eylea_data) > 0 else 0
            faricimab_pct = faricimab_count / len(faricimab_data) * 100 if len(faricimab_data) > 0 else 0
            
            print(f"{inj_count:<8} {eylea_count} ({eylea_pct:.1f}%)      {faricimab_count} ({faricimab_pct:.1f}%)      {total_count:<10}")
        
        # 分析注射负担
        print(f"\n💊 注射负担分析 (仅1年随访患者):")
        
        # 低注射负担 (≤2次)
        eylea_low = (eylea_data <= 2).sum()
        faricimab_low = (faricimab_data <= 2).sum()
        
        # 中等注射负担 (3-5次)
        eylea_medium = ((eylea_data >= 3) & (eylea_data <= 5)).sum()
        faricimab_medium = ((faricimab_data >= 3) & (faricimab_data <= 5)).sum()
        
        # 高注射负担 (≥6次)
        eylea_high = (eylea_data >= 6).sum()
        faricimab_high = (faricimab_data >= 6).sum()
        
        print(f"低注射负担 (≤2次):")
        print(f"  EYLEA: {eylea_low}/{len(eylea_data)} ({eylea_low/len(eylea_data)*100:.1f}%)")
        print(f"  FARICIMAB: {faricimab_low}/{len(faricimab_data)} ({faricimab_low/len(faricimab_data)*100:.1f}%)")
        
        print(f"中等注射负担 (3-5次):")
        print(f"  EYLEA: {eylea_medium}/{len(eylea_data)} ({eylea_medium/len(eylea_data)*100:.1f}%)")
        print(f"  FARICIMAB: {faricimab_medium}/{len(faricimab_data)} ({faricimab_medium/len(faricimab_data)*100:.1f}%)")
        
        print(f"高注射负担 (≥6次):")
        print(f"  EYLEA: {eylea_high}/{len(eylea_data)} ({eylea_high/len(eylea_data)*100:.1f}%)")
        print(f"  FARICIMAB: {faricimab_high}/{len(faricimab_data)} ({faricimab_high/len(faricimab_data)*100:.1f}%)")

def create_corrected_summary_table(eylea_data, faricimab_data, p_value):
    """创建修正版总结表"""
    if eylea_data is None or faricimab_data is None:
        return None
    
    summary_data = []
    
    # 基本统计
    summary_data.append({
        'Metric': 'Mean ± SD',
        'EYLEA (1-year FU)': f"{eylea_data.mean():.1f} ± {eylea_data.std():.1f}",
        'FARICIMAB (1-year FU)': f"{faricimab_data.mean():.1f} ± {faricimab_data.std():.1f}",
        'P-value': f"{p_value:.3f}"
    })
    
    summary_data.append({
        'Metric': 'Median (IQR)',
        'EYLEA (1-year FU)': f"{eylea_data.median():.1f} ({eylea_data.quantile(0.25):.1f}-{eylea_data.quantile(0.75):.1f})",
        'FARICIMAB (1-year FU)': f"{faricimab_data.median():.1f} ({faricimab_data.quantile(0.25):.1f}-{faricimab_data.quantile(0.75):.1f})",
        'P-value': ''
    })
    
    summary_data.append({
        'Metric': 'Range',
        'EYLEA (1-year FU)': f"{eylea_data.min():.0f}-{eylea_data.max():.0f}",
        'FARICIMAB (1-year FU)': f"{faricimab_data.min():.0f}-{faricimab_data.max():.0f}",
        'P-value': ''
    })
    
    summary_data.append({
        'Metric': 'Sample size, n',
        'EYLEA (1-year FU)': f"{len(eylea_data)}",
        'FARICIMAB (1-year FU)': f"{len(faricimab_data)}",
        'P-value': ''
    })
    
    # 注射负担分类
    summary_data.append({
        'Metric': '--- INJECTION BURDEN ---',
        'EYLEA (1-year FU)': '',
        'FARICIMAB (1-year FU)': '',
        'P-value': ''
    })
    
    # 低注射负担
    eylea_low = (eylea_data <= 2).sum()
    faricimab_low = (faricimab_data <= 2).sum()
    summary_data.append({
        'Metric': 'Low burden (≤2 injections), n (%)',
        'EYLEA (1-year FU)': f"{eylea_low} ({eylea_low/len(eylea_data)*100:.1f}%)",
        'FARICIMAB (1-year FU)': f"{faricimab_low} ({faricimab_low/len(faricimab_data)*100:.1f}%)",
        'P-value': ''
    })
    
    # 中等注射负担
    eylea_medium = ((eylea_data >= 3) & (eylea_data <= 5)).sum()
    faricimab_medium = ((faricimab_data >= 3) & (faricimab_data <= 5)).sum()
    summary_data.append({
        'Metric': 'Medium burden (3-5 injections), n (%)',
        'EYLEA (1-year FU)': f"{eylea_medium} ({eylea_medium/len(eylea_data)*100:.1f}%)",
        'FARICIMAB (1-year FU)': f"{faricimab_medium} ({faricimab_medium/len(faricimab_data)*100:.1f}%)",
        'P-value': ''
    })
    
    # 高注射负担
    eylea_high = (eylea_data >= 6).sum()
    faricimab_high = (faricimab_data >= 6).sum()
    summary_data.append({
        'Metric': 'High burden (≥6 injections), n (%)',
        'EYLEA (1-year FU)': f"{eylea_high} ({eylea_high/len(eylea_data)*100:.1f}%)",
        'FARICIMAB (1-year FU)': f"{faricimab_high} ({faricimab_high/len(faricimab_data)*100:.1f}%)",
        'P-value': ''
    })
    
    # 保存为CSV
    summary_df = pd.DataFrame(summary_data)
    summary_df.to_csv('Post_LP_Injection_Corrected_1Year_FU.csv', index=False, encoding='utf-8')
    
    return summary_df

def create_corrected_visualization(eylea_data, faricimab_data, p_value):
    """创建修正版可视化"""
    if eylea_data is None or faricimab_data is None:
        return
    
    print(f"\n📊 创建修正版可视化图表...")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
    
    # 1. 箱线图比较
    data_to_plot = [eylea_data, faricimab_data]
    labels = [f'EYLEA\n(n={len(eylea_data)})', f'FARICIMAB\n(n={len(faricimab_data)})']
    colors = ['#2E86AB', '#A23B72']
    
    bp = ax1.boxplot(data_to_plot, labels=labels, patch_artist=True, 
                     notch=True, showmeans=True)
    
    for patch, color in zip(bp['boxes'], colors):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)
    
    ax1.set_ylabel('Number of Injections after Loading Phase', fontsize=12, fontweight='bold')
    ax1.set_title('Post-Loading Phase Injection Frequency\n(1-Year Follow-up Patients Only)', 
                  fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    
    # 添加统计信息
    ax1.text(0.02, 0.98, f'EYLEA: {eylea_data.mean():.1f} ± {eylea_data.std():.1f}\n'
                         f'FARICIMAB: {faricimab_data.mean():.1f} ± {faricimab_data.std():.1f}\n'
                         f'P-value: {p_value:.3f}', 
             transform=ax1.transAxes, fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 2. 分布直方图
    bins = np.arange(0, max(eylea_data.max(), faricimab_data.max()) + 2) - 0.5
    
    ax2.hist(eylea_data, bins=bins, alpha=0.7, label=f'EYLEA (n={len(eylea_data)})', color=colors[0], density=True)
    ax2.hist(faricimab_data, bins=bins, alpha=0.7, label=f'FARICIMAB (n={len(faricimab_data)})', color=colors[1], density=True)
    
    ax2.set_xlabel('Number of Injections after Loading Phase', fontsize=12, fontweight='bold')
    ax2.set_ylabel('Density', fontsize=12, fontweight='bold')
    ax2.set_title('Distribution of Post-Loading Phase Injection\n(1-Year Follow-up Patients Only)', 
                  fontsize=14, fontweight='bold')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存多种格式
    plt.savefig('Post_LP_Injection_Corrected_1Year_FU.png', dpi=300, bbox_inches='tight')
    plt.savefig('Post_LP_Injection_Corrected_1Year_FU.pdf', dpi=300, bbox_inches='tight')
    plt.savefig('Post_LP_Injection_Corrected_1Year_FU.svg', dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """主函数"""
    
    # 加载并分析修正后的数据
    eylea_data, faricimab_data, p_value = load_and_analyze_corrected_data()
    
    if eylea_data is not None and faricimab_data is not None:
        # 分析分布
        analyze_injection_distribution_corrected(eylea_data, faricimab_data)
        
        # 创建可视化
        create_corrected_visualization(eylea_data, faricimab_data, p_value)
        
        # 创建总结表
        summary_df = create_corrected_summary_table(eylea_data, faricimab_data, p_value)
        
        print(f"\n" + "="*80)
        print("📋 修正版：Loading Phase后注射次数总结表 (仅1年随访患者)")
        print("="*80)
        
        for _, row in summary_df.iterrows():
            if row['Metric'].startswith('---'):
                print(f"\n{row['Metric']}")
            else:
                print(f"{row['Metric']:<40} {row['EYLEA (1-year FU)']:<25} {row['FARICIMAB (1-year FU)']:<25} {row['P-value']}")
        
        print(f"\n✅ 修正版分析完成！生成的文件:")
        print("- Post_LP_Injection_Corrected_1Year_FU.png/.pdf/.svg")
        print("- Post_LP_Injection_Corrected_1Year_FU.csv")
        
        # 临床意义解释
        print(f"\n🏥 临床意义 (仅1年随访患者):")
        if p_value < 0.001:
            print(f"- 两组间LP后注射次数存在极显著差异 (p<0.001)")
        elif p_value < 0.05:
            print(f"- 两组间LP后注射次数存在统计学显著差异 (p={p_value:.3f})")
        else:
            print(f"- 两组间LP后注射次数无统计学显著差异 (p={p_value:.3f})")
        
        print(f"- EYLEA组平均需要 {eylea_data.mean():.1f} 次额外注射")
        print(f"- FARICIMAB组平均需要 {faricimab_data.mean():.1f} 次额外注射")
        
        if faricimab_data.mean() < eylea_data.mean():
            reduction = eylea_data.mean() - faricimab_data.mean()
            reduction_pct = reduction / eylea_data.mean() * 100
            print(f"- FARICIMAB相比EYLEA减少了 {reduction:.1f} 次注射 ({reduction_pct:.1f}%的减少)")
        
        print(f"- 本分析仅包含完成1年随访的患者，结果更加可靠")
    
    else:
        print("❌ 未找到有效的注射次数数据或1年随访数据")

if __name__ == "__main__":
    main()
