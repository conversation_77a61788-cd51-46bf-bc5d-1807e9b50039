#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF Files Summary for nAMD Analysis
Lists all generated PDF files with descriptions
"""

import os
import glob
from datetime import datetime

def get_file_info(filename):
    """Get file size and modification time"""
    if os.path.exists(filename):
        size_mb = os.path.getsize(filename) / (1024 * 1024)
        mod_time = datetime.fromtimestamp(os.path.getmtime(filename))
        return size_mb, mod_time
    return None, None

def main():
    print("="*80)
    print("nAMD 分析 - PDF 文件总结")
    print("="*80)
    
    # Define expected PDF files with descriptions
    pdf_files = {
        'Enhanced_Morphology_Comparison.pdf': '增强形态学比较图 - 负荷期后改善率',
        'Enhanced_Year1_Comparison.pdf': '增强1年随访比较图 - 长期改善率',
        'Morphology_Timeline_Analysis.pdf': '形态学时间线分析 - 药物比较',
        'MNV_Subgroup_Timeline_Analysis.pdf': 'MNV亚组时间线分析 - 按MNV类型分组',
        'Drug_MNV1_Timeline_Analysis.pdf': 'MNV1型药物比较分析',
        'Drug_MNV2_Timeline_Analysis.pdf': 'MNV2型药物比较分析',
        'Drug_MNV3_Timeline_Analysis.pdf': 'MNV3型药物比较分析'
    }
    
    print("\n📊 生成的PDF文件列表:")
    print("-" * 80)
    
    total_size = 0
    existing_files = 0
    
    for filename, description in pdf_files.items():
        size_mb, mod_time = get_file_info(filename)
        if size_mb is not None:
            existing_files += 1
            total_size += size_mb
            print(f"✓ {filename}")
            print(f"  📝 {description}")
            print(f"  📏 大小: {size_mb:.1f} MB")
            print(f"  🕒 修改时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print()
        else:
            print(f"✗ {filename} - 文件不存在")
            print(f"  📝 {description}")
            print()
    
    print("="*80)
    print("📈 统计信息")
    print("="*80)
    print(f"生成的PDF文件数量: {existing_files}/{len(pdf_files)}")
    print(f"总文件大小: {total_size:.1f} MB")
    
    if existing_files == len(pdf_files):
        print("✅ 所有PDF文件已成功生成！")
    else:
        missing = len(pdf_files) - existing_files
        print(f"⚠️  缺少 {missing} 个PDF文件")
    
    print("\n🎯 PDF文件特点:")
    print("- 高质量300 DPI分辨率，适合学术发表")
    print("- 矢量格式，可任意缩放不失真")
    print("- 可直接在PDF阅读器中编辑和标注")
    print("- 兼容所有操作系统和软件")
    
    print("\n📋 使用建议:")
    print("- 使用Adobe Acrobat或其他PDF编辑器进行标注")
    print("- 可直接插入到Word文档或LaTeX论文中")
    print("- 适合会议展示和学术交流")
    print("- 保持原始PNG文件作为备份")
    
    # Check for any additional PDF files
    all_pdfs = glob.glob('*.pdf')
    additional_pdfs = [f for f in all_pdfs if f not in pdf_files.keys()]
    
    if additional_pdfs:
        print(f"\n📁 其他PDF文件 ({len(additional_pdfs)}个):")
        print("-" * 40)
        for pdf in additional_pdfs:
            size_mb, mod_time = get_file_info(pdf)
            print(f"• {pdf} ({size_mb:.1f} MB)")

if __name__ == "__main__":
    main()
