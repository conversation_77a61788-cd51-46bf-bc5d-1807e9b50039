#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Final Format Summary - PNG, PDF, SVG Files Overview
Complete summary of all generated visualization files
"""

import os
import glob
from datetime import datetime

def get_file_info(filename):
    """Get file size and modification time"""
    if os.path.exists(filename):
        size_kb = os.path.getsize(filename) / 1024
        mod_time = datetime.fromtimestamp(os.path.getmtime(filename))
        return size_kb, mod_time
    return None, None

def main():
    print("="*90)
    print("🎯 nAMD 分析图表 - 完整格式总结报告")
    print("="*90)
    
    # Define expected files
    base_names = [
        'Enhanced_Morphology_Comparison',
        'Enhanced_Year1_Comparison',
        'MNV_Subgroup_Timeline_Analysis',
        'Drug_MNV1_Timeline_Analysis',
        'Drug_MNV2_Timeline_Analysis',
        'Drug_MNV3_Timeline_Analysis'
    ]
    
    formats = ['png', 'pdf', 'svg']
    format_descriptions = {
        'png': '📸 PNG - 位图格式，快速预览',
        'pdf': '📄 PDF - Adobe可编辑，学术发表',
        'svg': '🎨 SVG - 矢量格式，网页嵌入'
    }
    
    print("\n📊 文件生成状态检查:")
    print("-" * 90)
    
    total_files = 0
    total_size = 0
    format_stats = {fmt: {'count': 0, 'size': 0} for fmt in formats}
    
    for base_name in base_names:
        print(f"\n📋 {base_name}:")
        
        for fmt in formats:
            filename = f"{base_name}.{fmt}"
            size_kb, mod_time = get_file_info(filename)
            
            if size_kb is not None:
                total_files += 1
                total_size += size_kb
                format_stats[fmt]['count'] += 1
                format_stats[fmt]['size'] += size_kb
                
                print(f"   ✅ {format_descriptions[fmt]}")
                print(f"      📏 {size_kb:.1f} KB | 🕒 {mod_time.strftime('%H:%M:%S')}")
            else:
                print(f"   ❌ {format_descriptions[fmt]} - 文件不存在")
    
    print("\n" + "="*90)
    print("📈 统计汇总")
    print("="*90)
    
    print(f"\n📊 总体统计:")
    print(f"- 图表数量: {len(base_names)} 个")
    print(f"- 生成文件: {total_files} 个")
    print(f"- 总文件大小: {total_size:.1f} KB ({total_size/1024:.1f} MB)")
    print(f"- 完整性: {total_files}/{len(base_names)*len(formats)} ({total_files/(len(base_names)*len(formats))*100:.1f}%)")
    
    print(f"\n📋 格式分布:")
    for fmt in formats:
        count = format_stats[fmt]['count']
        size = format_stats[fmt]['size']
        avg_size = size / count if count > 0 else 0
        print(f"- {format_descriptions[fmt]}: {count} 个文件, {size:.1f} KB (平均 {avg_size:.1f} KB)")
    
    print("\n" + "="*90)
    print("🎯 格式特点和使用建议")
    print("="*90)
    
    print("\n📸 PNG 格式:")
    print("✅ 优点: 广泛支持, 快速加载, 适合网页显示")
    print("✅ 用途: 快速预览, 邮件附件, 社交媒体分享")
    print("⚠️  注意: 位图格式, 放大会失真")
    
    print("\n📄 PDF 格式:")
    print("✅ 优点: Adobe Acrobat可编辑, 高质量打印, 学术标准")
    print("✅ 用途: 论文发表, 正式报告, 印刷品制作")
    print("🔧 编辑: Adobe Acrobat Pro, 文本完全可编辑")
    
    print("\n🎨 SVG 格式:")
    print("✅ 优点: 矢量图形, 无限缩放, 文本可编辑, 网页原生支持")
    print("✅ 用途: 网页嵌入, 响应式设计, 交互式可视化")
    print("🔧 编辑: Adobe Illustrator, Inkscape, 文本编辑器")
    
    print("\n" + "="*90)
    print("💡 使用场景推荐")
    print("="*90)
    
    scenarios = [
        ("📖 学术论文发表", "使用 PDF 格式，Adobe Acrobat编辑标题和标签"),
        ("🌐 网站展示", "使用 SVG 格式，响应式设计，支持交互"),
        ("📱 移动端查看", "使用 PNG 格式，快速加载，兼容性好"),
        ("🖨️  高质量打印", "使用 PDF 或 SVG 格式，矢量图形不失真"),
        ("📧 邮件分享", "使用 PNG 格式，文件小，预览方便"),
        ("🎨 图形设计", "使用 SVG 格式，Adobe Illustrator编辑"),
        ("📊 数据报告", "使用 PDF 格式，专业外观，易于标注"),
        ("💻 演示文稿", "使用 PNG 或 SVG 格式，根据需要选择")
    ]
    
    for scenario, recommendation in scenarios:
        print(f"\n{scenario}:")
        print(f"   💡 {recommendation}")
    
    print("\n" + "="*90)
    print("🔧 技术规格")
    print("="*90)
    
    print("\n📐 图像规格:")
    print("- 分辨率: 300 DPI (适合印刷)")
    print("- 字体: TrueType (Adobe可编辑)")
    print("- 颜色: RGB色彩空间")
    print("- 边距: 自动裁剪 (bbox_inches='tight')")
    
    print("\n🎨 设计特点:")
    print("- 统计显著性标注 (*, **, ***, ns)")
    print("- 95%置信区间误差棒")
    print("- 专业配色方案")
    print("- 清晰的图例和标签")
    print("- 适合学术发表的布局")
    
    print(f"\n🎉 所有 {len(base_names)} 个图表已成功生成为三种格式！")
    print("现在您可以根据不同需求选择最适合的格式使用。")

if __name__ == "__main__":
    main()
