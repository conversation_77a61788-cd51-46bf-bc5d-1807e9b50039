#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Detailed Morphological Analysis for nAMD Study
包括改善率、完全消除率、总积液率等详细指标

Author: AI Assistant
Date: 2025-01-07
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import scipy.stats as stats
from scipy.stats import chi2_contingency
import warnings
warnings.filterwarnings('ignore')

# Set English fonts
plt.rcParams['font.family'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

# Set figure style
sns.set_style("whitegrid")
sns.set_palette("Set2")

def load_data():
    """Load cleaned data"""
    df = pd.read_csv('namd_cleaned_data.csv')
    useful_cols = [col for col in df.columns if not col.startswith('Unnamed')]
    return df[useful_cols]

def calculate_detailed_morphology_metrics(df):
    """Calculate detailed morphological metrics"""
    print("=== Detailed Morphological Analysis ===")
    
    # Convert morphological data to numeric
    morphology_vars = ['IRF', 'SRF', 'SHRM', 'RPE rupture', 'Hemorrhage']
    timepoints = ['BL', 'Post-LP', 'Year 1']
    
    for var in morphology_vars:
        for tp in timepoints:
            col_name = f"{var} ({tp})"
            if col_name in df.columns:
                df[col_name] = pd.to_numeric(df[col_name], errors='coerce')
    
    results = []
    
    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df[df['Drug'] == drug].copy()
        
        print(f"\n{drug} Group Analysis (n={len(drug_data)}):")
        print("=" * 50)
        
        for var in morphology_vars:
            bl_col = f"{var} (BL)"
            postlp_col = f"{var} (Post-LP)"
            year1_col = f"{var} (Year 1)"
            
            if bl_col in df.columns and postlp_col in df.columns:
                # Baseline to Post-LP analysis
                bl_data = drug_data[bl_col].dropna()
                postlp_data = drug_data[postlp_col].dropna()
                
                # Find patients with both baseline and post-LP data
                paired_data = drug_data[[bl_col, postlp_col]].dropna()
                
                if len(paired_data) > 0:
                    bl_positive = paired_data[bl_col].sum()
                    postlp_positive = paired_data[postlp_col].sum()
                    total_paired = len(paired_data)
                    
                    # Calculate metrics
                    baseline_prevalence = (bl_positive / total_paired) * 100
                    postlp_prevalence = (postlp_positive / total_paired) * 100
                    
                    # Improvement rate (among those with baseline positive)
                    bl_positive_patients = paired_data[paired_data[bl_col] == 1]
                    if len(bl_positive_patients) > 0:
                        improved_patients = bl_positive_patients[bl_positive_patients[postlp_col] == 0]
                        improvement_rate = (len(improved_patients) / len(bl_positive_patients)) * 100
                        
                        # Complete resolution rate
                        complete_resolution_rate = improvement_rate  # Same as improvement rate for binary data
                    else:
                        improvement_rate = 0
                        complete_resolution_rate = 0
                    
                    # Relative reduction
                    if bl_positive > 0:
                        relative_reduction = ((bl_positive - postlp_positive) / bl_positive) * 100
                    else:
                        relative_reduction = 0
                    
                    # Absolute reduction
                    absolute_reduction = baseline_prevalence - postlp_prevalence
                    
                    # New cases (among those negative at baseline)
                    bl_negative_patients = paired_data[paired_data[bl_col] == 0]
                    if len(bl_negative_patients) > 0:
                        new_cases = bl_negative_patients[bl_negative_patients[postlp_col] == 1]
                        new_case_rate = (len(new_cases) / len(bl_negative_patients)) * 100
                    else:
                        new_case_rate = 0
                    
                    results.append({
                        'Drug': drug,
                        'Variable': var,
                        'Timepoint_Comparison': 'BL_to_PostLP',
                        'N_Paired': total_paired,
                        'Baseline_Positive': bl_positive,
                        'Baseline_Prevalence': baseline_prevalence,
                        'PostLP_Positive': postlp_positive,
                        'PostLP_Prevalence': postlp_prevalence,
                        'Improvement_Rate': improvement_rate,
                        'Complete_Resolution_Rate': complete_resolution_rate,
                        'Relative_Reduction': relative_reduction,
                        'Absolute_Reduction': absolute_reduction,
                        'New_Case_Rate': new_case_rate
                    })
                    
                    print(f"\n{var} (Baseline to Post-LP):")
                    print(f"  Paired patients: {total_paired}")
                    print(f"  Baseline prevalence: {baseline_prevalence:.1f}% ({bl_positive}/{total_paired})")
                    print(f"  Post-LP prevalence: {postlp_prevalence:.1f}% ({postlp_positive}/{total_paired})")
                    print(f"  Improvement rate: {improvement_rate:.1f}%")
                    print(f"  Complete resolution rate: {complete_resolution_rate:.1f}%")
                    print(f"  Relative reduction: {relative_reduction:.1f}%")
                    print(f"  Absolute reduction: {absolute_reduction:.1f} percentage points")
                    print(f"  New case rate: {new_case_rate:.1f}%")
    
    return pd.DataFrame(results)

def calculate_total_fluid_metrics(df):
    """Calculate total fluid (SRF + IRF) metrics"""
    print("\n=== Total Fluid Analysis (SRF + IRF Combined) ===")
    
    # Create total fluid variables
    timepoints = ['BL', 'Post-LP', 'Year 1']
    
    for tp in timepoints:
        srf_col = f"SRF ({tp})"
        irf_col = f"IRF ({tp})"
        total_fluid_col = f"Total_Fluid ({tp})"
        
        if srf_col in df.columns and irf_col in df.columns:
            # Total fluid = 1 if either SRF or IRF is present
            df[total_fluid_col] = ((pd.to_numeric(df[srf_col], errors='coerce').fillna(0) == 1) | 
                                  (pd.to_numeric(df[irf_col], errors='coerce').fillna(0) == 1)).astype(int)
    
    total_fluid_results = []
    
    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df[df['Drug'] == drug].copy()
        
        print(f"\n{drug} Group Total Fluid Analysis:")
        print("-" * 40)
        
        # Baseline to Post-LP
        bl_col = "Total_Fluid (BL)"
        postlp_col = "Total_Fluid (Post-LP)"
        
        if bl_col in df.columns and postlp_col in df.columns:
            paired_data = drug_data[[bl_col, postlp_col]].dropna()
            
            if len(paired_data) > 0:
                bl_positive = paired_data[bl_col].sum()
                postlp_positive = paired_data[postlp_col].sum()
                total_paired = len(paired_data)
                
                baseline_prevalence = (bl_positive / total_paired) * 100
                postlp_prevalence = (postlp_positive / total_paired) * 100
                
                # Dry retina achievement rate (among those with baseline fluid)
                bl_positive_patients = paired_data[paired_data[bl_col] == 1]
                if len(bl_positive_patients) > 0:
                    dry_retina_patients = bl_positive_patients[bl_positive_patients[postlp_col] == 0]
                    dry_retina_rate = (len(dry_retina_patients) / len(bl_positive_patients)) * 100
                else:
                    dry_retina_rate = 0
                
                # Relative reduction
                if bl_positive > 0:
                    relative_reduction = ((bl_positive - postlp_positive) / bl_positive) * 100
                else:
                    relative_reduction = 0
                
                absolute_reduction = baseline_prevalence - postlp_prevalence
                
                total_fluid_results.append({
                    'Drug': drug,
                    'N_Paired': total_paired,
                    'Baseline_Total_Fluid': bl_positive,
                    'Baseline_Prevalence': baseline_prevalence,
                    'PostLP_Total_Fluid': postlp_positive,
                    'PostLP_Prevalence': postlp_prevalence,
                    'Dry_Retina_Rate': dry_retina_rate,
                    'Relative_Reduction': relative_reduction,
                    'Absolute_Reduction': absolute_reduction
                })
                
                print(f"  Paired patients: {total_paired}")
                print(f"  Baseline total fluid: {baseline_prevalence:.1f}% ({bl_positive}/{total_paired})")
                print(f"  Post-LP total fluid: {postlp_prevalence:.1f}% ({postlp_positive}/{total_paired})")
                print(f"  Dry retina achievement rate: {dry_retina_rate:.1f}%")
                print(f"  Relative reduction: {relative_reduction:.1f}%")
                print(f"  Absolute reduction: {absolute_reduction:.1f} percentage points")
    
    return pd.DataFrame(total_fluid_results)

def create_detailed_morphology_plots(detailed_results, total_fluid_results):
    """Create detailed morphological analysis plots"""
    print("\nCreating detailed morphological plots...")
    
    # 1. Improvement rates comparison
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # Filter for main morphological indicators
    main_vars = ['IRF', 'SRF', 'SHRM', 'Hemorrhage']
    plot_data = detailed_results[detailed_results['Variable'].isin(main_vars)]
    
    # Improvement rates
    improvement_data = plot_data.pivot(index='Variable', columns='Drug', values='Improvement_Rate')
    improvement_data.plot(kind='bar', ax=axes[0,0], color=['#2E86AB', '#A23B72'])
    axes[0,0].set_title('Improvement Rates by Morphological Indicator', fontweight='bold')
    axes[0,0].set_ylabel('Improvement Rate (%)')
    axes[0,0].legend(title='Treatment')
    axes[0,0].tick_params(axis='x', rotation=45)
    
    # Complete resolution rates
    resolution_data = plot_data.pivot(index='Variable', columns='Drug', values='Complete_Resolution_Rate')
    resolution_data.plot(kind='bar', ax=axes[0,1], color=['#2E86AB', '#A23B72'])
    axes[0,1].set_title('Complete Resolution Rates', fontweight='bold')
    axes[0,1].set_ylabel('Complete Resolution Rate (%)')
    axes[0,1].legend(title='Treatment')
    axes[0,1].tick_params(axis='x', rotation=45)
    
    # Relative reduction
    reduction_data = plot_data.pivot(index='Variable', columns='Drug', values='Relative_Reduction')
    reduction_data.plot(kind='bar', ax=axes[1,0], color=['#2E86AB', '#A23B72'])
    axes[1,0].set_title('Relative Reduction in Prevalence', fontweight='bold')
    axes[1,0].set_ylabel('Relative Reduction (%)')
    axes[1,0].legend(title='Treatment')
    axes[1,0].tick_params(axis='x', rotation=45)
    
    # Total fluid analysis
    if not total_fluid_results.empty:
        x = np.arange(len(total_fluid_results))
        width = 0.35
        
        baseline = total_fluid_results['Baseline_Prevalence']
        postlp = total_fluid_results['PostLP_Prevalence']
        drugs = total_fluid_results['Drug']
        
        axes[1,1].bar(x - width/2, baseline, width, label='Baseline', alpha=0.8)
        axes[1,1].bar(x + width/2, postlp, width, label='Post-LP', alpha=0.8)
        
        axes[1,1].set_title('Total Fluid (SRF + IRF) Prevalence', fontweight='bold')
        axes[1,1].set_ylabel('Prevalence (%)')
        axes[1,1].set_xlabel('Treatment Group')
        axes[1,1].set_xticks(x)
        axes[1,1].set_xticklabels(drugs)
        axes[1,1].legend()
        
        # Add dry retina rates as text
        for i, (drug, rate) in enumerate(zip(drugs, total_fluid_results['Dry_Retina_Rate'])):
            axes[1,1].text(i, max(baseline.max(), postlp.max()) + 5, 
                          f'Dry Retina: {rate:.1f}%', 
                          ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('Figure5_Detailed_Morphology_Analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. Create waterfall chart for fluid resolution
    fig, axes = plt.subplots(1, 2, figsize=(14, 6))
    
    for i, drug in enumerate(['EYLEA', 'FARICIMAB']):
        drug_data = plot_data[plot_data['Drug'] == drug]
        
        variables = drug_data['Variable'].tolist()
        baseline_prev = drug_data['Baseline_Prevalence'].tolist()
        postlp_prev = drug_data['PostLP_Prevalence'].tolist()
        
        x = np.arange(len(variables))
        width = 0.35
        
        bars1 = axes[i].bar(x - width/2, baseline_prev, width, label='Baseline', alpha=0.8)
        bars2 = axes[i].bar(x + width/2, postlp_prev, width, label='Post-LP', alpha=0.8)
        
        # Add improvement percentages
        for j, (bl, pl, var) in enumerate(zip(baseline_prev, postlp_prev, variables)):
            improvement = drug_data[drug_data['Variable'] == var]['Improvement_Rate'].iloc[0]
            axes[i].text(j, max(bl, pl) + 2, f'{improvement:.0f}%', 
                        ha='center', va='bottom', fontweight='bold', fontsize=10)
        
        axes[i].set_title(f'{drug}\nMorphological Indicator Prevalence', fontweight='bold')
        axes[i].set_ylabel('Prevalence (%)')
        axes[i].set_xticks(x)
        axes[i].set_xticklabels(variables, rotation=45)
        axes[i].legend()
    
    plt.tight_layout()
    plt.savefig('Figure6_Morphology_Waterfall.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Detailed morphological plots saved:")
    print("- Figure5_Detailed_Morphology_Analysis.png")
    print("- Figure6_Morphology_Waterfall.png")

def statistical_comparison_morphology(detailed_results, total_fluid_results):
    """Perform statistical comparisons between groups"""
    print("\n=== Statistical Comparisons Between Groups ===")
    
    comparison_results = []
    
    # Compare improvement rates between groups
    main_vars = ['IRF', 'SRF', 'SHRM', 'Hemorrhage']
    
    for var in main_vars:
        var_data = detailed_results[detailed_results['Variable'] == var]
        
        if len(var_data) == 2:  # Both EYLEA and FARICIMAB data available
            eylea_data = var_data[var_data['Drug'] == 'EYLEA'].iloc[0]
            faricimab_data = var_data[var_data['Drug'] == 'FARICIMAB'].iloc[0]
            
            # Chi-square test for improvement rates
            # Create contingency table
            eylea_improved = int(eylea_data['Baseline_Positive'] * eylea_data['Improvement_Rate'] / 100)
            eylea_not_improved = int(eylea_data['Baseline_Positive'] - eylea_improved)
            faricimab_improved = int(faricimab_data['Baseline_Positive'] * faricimab_data['Improvement_Rate'] / 100)
            faricimab_not_improved = int(faricimab_data['Baseline_Positive'] - faricimab_improved)
            
            if (eylea_improved + eylea_not_improved > 0) and (faricimab_improved + faricimab_not_improved > 0):
                contingency = [[eylea_improved, eylea_not_improved],
                              [faricimab_improved, faricimab_not_improved]]
                
                try:
                    chi2, p_val = chi2_contingency(contingency)[:2]
                except:
                    p_val = np.nan
                
                comparison_results.append({
                    'Variable': var,
                    'Metric': 'Improvement_Rate',
                    'EYLEA_Value': eylea_data['Improvement_Rate'],
                    'FARICIMAB_Value': faricimab_data['Improvement_Rate'],
                    'P_value': p_val,
                    'Difference': faricimab_data['Improvement_Rate'] - eylea_data['Improvement_Rate']
                })
                
                print(f"\n{var} Improvement Rate Comparison:")
                print(f"  EYLEA: {eylea_data['Improvement_Rate']:.1f}% ({eylea_improved}/{int(eylea_data['Baseline_Positive'])})")
                print(f"  FARICIMAB: {faricimab_data['Improvement_Rate']:.1f}% ({faricimab_improved}/{int(faricimab_data['Baseline_Positive'])})")
                print(f"  Difference: {faricimab_data['Improvement_Rate'] - eylea_data['Improvement_Rate']:+.1f} percentage points")
                print(f"  P-value: {p_val:.3f}" if not np.isnan(p_val) else "  P-value: N/A")
    
    # Compare total fluid dry retina rates
    if len(total_fluid_results) == 2:
        eylea_dry = total_fluid_results[total_fluid_results['Drug'] == 'EYLEA'].iloc[0]
        faricimab_dry = total_fluid_results[total_fluid_results['Drug'] == 'FARICIMAB'].iloc[0]
        
        print(f"\nTotal Fluid (Dry Retina Achievement) Comparison:")
        print(f"  EYLEA: {eylea_dry['Dry_Retina_Rate']:.1f}%")
        print(f"  FARICIMAB: {faricimab_dry['Dry_Retina_Rate']:.1f}%")
        print(f"  Difference: {faricimab_dry['Dry_Retina_Rate'] - eylea_dry['Dry_Retina_Rate']:+.1f} percentage points")
    
    return pd.DataFrame(comparison_results)

if __name__ == "__main__":
    # Load data
    df = load_data()
    
    # Detailed morphological analysis
    detailed_results = calculate_detailed_morphology_metrics(df)
    
    # Total fluid analysis
    total_fluid_results = calculate_total_fluid_metrics(df)
    
    # Statistical comparisons
    comparison_results = statistical_comparison_morphology(detailed_results, total_fluid_results)
    
    # Create detailed plots
    create_detailed_morphology_plots(detailed_results, total_fluid_results)
    
    # Save results
    detailed_results.to_csv('Table3_Detailed_Morphology_Metrics.csv', index=False)
    total_fluid_results.to_csv('Table4_Total_Fluid_Analysis.csv', index=False)
    comparison_results.to_csv('Table5_Statistical_Comparisons.csv', index=False)
    
    print(f"\nDetailed morphological analysis completed!")
    print("Generated files:")
    print("- Table3_Detailed_Morphology_Metrics.csv")
    print("- Table4_Total_Fluid_Analysis.csv")
    print("- Table5_Statistical_Comparisons.csv")
    print("- Figure5_Detailed_Morphology_Analysis.png")
    print("- Figure6_Morphology_Waterfall.png")
