#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Convert PNG charts to PDF format for publication
Converts all generated analysis charts from PNG to high-quality PDF format
"""

import os
import glob
from PIL import Image
import matplotlib.pyplot as plt
import matplotlib.image as mpimg
from matplotlib.backends.backend_pdf import PdfPages
import warnings
warnings.filterwarnings('ignore')

def convert_png_to_pdf(png_file, pdf_file):
    """Convert a single PNG file to PDF with high quality"""
    try:
        # Read the PNG image
        img = mpimg.imread(png_file)
        
        # Create figure with appropriate size
        fig, ax = plt.subplots(figsize=(16, 12))
        ax.imshow(img)
        ax.axis('off')  # Remove axes
        
        # Save as PDF with high quality
        plt.savefig(pdf_file, format='pdf', dpi=300, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        plt.close()
        
        print(f"✓ Converted: {png_file} → {pdf_file}")
        return True
        
    except Exception as e:
        print(f"✗ Error converting {png_file}: {str(e)}")
        return False

def create_combined_pdf(png_files, output_pdf):
    """Create a combined PDF with multiple charts"""
    try:
        with PdfPages(output_pdf) as pdf:
            for png_file in png_files:
                if os.path.exists(png_file):
                    # Read the PNG image
                    img = mpimg.imread(png_file)
                    
                    # Create figure with appropriate size
                    fig, ax = plt.subplots(figsize=(16, 12))
                    ax.imshow(img)
                    ax.axis('off')  # Remove axes
                    
                    # Add title based on filename
                    title = os.path.splitext(os.path.basename(png_file))[0].replace('_', ' ')
                    fig.suptitle(title, fontsize=16, fontweight='bold', y=0.98)
                    
                    # Save this page to PDF
                    pdf.savefig(fig, dpi=300, bbox_inches='tight', 
                               facecolor='white', edgecolor='none')
                    plt.close()
        
        print(f"✓ Created combined PDF: {output_pdf}")
        return True
        
    except Exception as e:
        print(f"✗ Error creating combined PDF: {str(e)}")
        return False

def main():
    """Main function to convert all PNG charts to PDF"""
    
    print("="*80)
    print("PNG TO PDF CONVERSION TOOL")
    print("="*80)
    
    # List of expected PNG files from our analyses
    png_files = [
        'Enhanced_Morphology_Comparison.png',
        'Enhanced_Year1_Comparison.png',
        'Morphology_Timeline_Analysis.png',
        'MNV_Subgroup_Timeline_Analysis.png',
        'Drug_MNV1_Timeline_Analysis.png',
        'Drug_MNV2_Timeline_Analysis.png',
        'Drug_MNV3_Timeline_Analysis.png'
    ]
    
    # Check which files exist
    existing_files = []
    missing_files = []
    
    for png_file in png_files:
        if os.path.exists(png_file):
            existing_files.append(png_file)
        else:
            missing_files.append(png_file)
    
    print(f"\nFound {len(existing_files)} PNG files to convert:")
    for file in existing_files:
        print(f"  • {file}")
    
    if missing_files:
        print(f"\nMissing {len(missing_files)} files:")
        for file in missing_files:
            print(f"  • {file}")
    
    print("\n" + "-"*80)
    print("CONVERTING INDIVIDUAL FILES TO PDF")
    print("-"*80)
    
    # Convert each PNG to individual PDF
    converted_count = 0
    for png_file in existing_files:
        pdf_file = png_file.replace('.png', '.pdf')
        if convert_png_to_pdf(png_file, pdf_file):
            converted_count += 1
    
    print(f"\n✓ Successfully converted {converted_count} files to individual PDFs")
    
    # Create combined PDFs by category
    print("\n" + "-"*80)
    print("CREATING COMBINED PDF FILES")
    print("-"*80)
    
    # Group files by analysis type
    morphology_files = [f for f in existing_files if 'Morphology' in f and 'Timeline' not in f]
    timeline_files = [f for f in existing_files if 'Timeline' in f]
    mnv_drug_files = [f for f in existing_files if 'Drug_MNV' in f]
    
    # Create combined PDFs
    if morphology_files:
        create_combined_pdf(morphology_files, 'Combined_Morphology_Analysis.pdf')
    
    if timeline_files:
        create_combined_pdf(timeline_files, 'Combined_Timeline_Analysis.pdf')
    
    if mnv_drug_files:
        create_combined_pdf(mnv_drug_files, 'Combined_Drug_MNV_Analysis.pdf')
    
    # Create master combined PDF with all charts
    if existing_files:
        create_combined_pdf(existing_files, 'Complete_nAMD_Analysis_Report.pdf')
    
    print("\n" + "="*80)
    print("CONVERSION SUMMARY")
    print("="*80)
    
    print(f"Individual PDF files created: {converted_count}")
    print("\nCombined PDF files created:")
    
    combined_pdfs = [
        'Combined_Morphology_Analysis.pdf',
        'Combined_Timeline_Analysis.pdf', 
        'Combined_Drug_MNV_Analysis.pdf',
        'Complete_nAMD_Analysis_Report.pdf'
    ]
    
    for pdf_file in combined_pdfs:
        if os.path.exists(pdf_file):
            print(f"  ✓ {pdf_file}")
        else:
            print(f"  ✗ {pdf_file} (not created)")
    
    print("\n" + "="*80)
    print("PDF CONVERSION COMPLETED SUCCESSFULLY!")
    print("="*80)
    
    # List all PDF files in current directory
    pdf_files = glob.glob('*.pdf')
    if pdf_files:
        print(f"\nAll PDF files in current directory ({len(pdf_files)} files):")
        for pdf_file in sorted(pdf_files):
            file_size = os.path.getsize(pdf_file) / (1024*1024)  # Size in MB
            print(f"  • {pdf_file} ({file_size:.1f} MB)")

if __name__ == "__main__":
    main()
