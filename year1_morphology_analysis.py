#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Year 1 Follow-up Morphological Analysis for nAMD Study
1年随访形态学详细分析，包括长期改善率和维持率

Author: AI Assistant
Date: 2025-01-07
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import scipy.stats as stats
from scipy.stats import chi2_contingency
import warnings
warnings.filterwarnings('ignore')

# Set English fonts
plt.rcParams['font.family'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

# Set figure style
sns.set_style("whitegrid")
sns.set_palette("Set2")

def load_data():
    """Load cleaned data"""
    df = pd.read_csv('namd_cleaned_data.csv')
    useful_cols = [col for col in df.columns if not col.startswith('Unnamed')]
    return df[useful_cols]

def analyze_year1_morphology(df):
    """Analyze Year 1 morphological outcomes"""
    print("=== Year 1 Follow-up Morphological Analysis ===")
    
    # Convert morphological data to numeric
    morphology_vars = ['IRF', 'SRF', 'SHRM', 'RPE rupture', 'Hemorrhage']
    timepoints = ['BL', 'Post-LP', 'Year 1']
    
    for var in morphology_vars:
        for tp in timepoints:
            col_name = f"{var} ({tp})"
            if col_name in df.columns:
                df[col_name] = pd.to_numeric(df[col_name], errors='coerce')
    
    # Check data availability
    print("\nData Availability for Year 1 Follow-up:")
    print("=" * 50)
    
    year1_availability = {}
    for var in morphology_vars:
        year1_col = f"{var} (Year 1)"
        if year1_col in df.columns:
            eylea_available = df[df['Drug'] == 'EYLEA'][year1_col].notna().sum()
            faricimab_available = df[df['Drug'] == 'FARICIMAB'][year1_col].notna().sum()
            total_available = df[year1_col].notna().sum()
            
            year1_availability[var] = {
                'EYLEA': eylea_available,
                'FARICIMAB': faricimab_available,
                'Total': total_available
            }
            
            print(f"{var} (Year 1):")
            print(f"  EYLEA: {eylea_available}/86 ({eylea_available/86*100:.1f}%)")
            print(f"  FARICIMAB: {faricimab_available}/86 ({faricimab_available/86*100:.1f}%)")
            print(f"  Total: {total_available}/172 ({total_available/172*100:.1f}%)")
            print()
    
    return year1_availability

def calculate_year1_detailed_metrics(df):
    """Calculate detailed Year 1 metrics"""
    print("\n=== Detailed Year 1 Morphological Metrics ===")
    
    morphology_vars = ['IRF', 'SRF', 'SHRM', 'RPE rupture', 'Hemorrhage']
    results = []
    
    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df[df['Drug'] == drug].copy()
        
        print(f"\n{drug} Group Year 1 Analysis:")
        print("=" * 40)
        
        for var in morphology_vars:
            bl_col = f"{var} (BL)"
            postlp_col = f"{var} (Post-LP)"
            year1_col = f"{var} (Year 1)"
            
            if all(col in df.columns for col in [bl_col, postlp_col, year1_col]):
                # Three-timepoint analysis (BL -> Post-LP -> Year 1)
                three_point_data = drug_data[[bl_col, postlp_col, year1_col]].dropna()
                
                if len(three_point_data) > 0:
                    bl_positive = three_point_data[bl_col].sum()
                    postlp_positive = three_point_data[postlp_col].sum()
                    year1_positive = three_point_data[year1_col].sum()
                    total_patients = len(three_point_data)
                    
                    # Calculate prevalences
                    bl_prevalence = (bl_positive / total_patients) * 100
                    postlp_prevalence = (postlp_positive / total_patients) * 100
                    year1_prevalence = (year1_positive / total_patients) * 100
                    
                    # Long-term improvement rate (BL to Year 1)
                    bl_positive_patients = three_point_data[three_point_data[bl_col] == 1]
                    if len(bl_positive_patients) > 0:
                        longterm_improved = bl_positive_patients[bl_positive_patients[year1_col] == 0]
                        longterm_improvement_rate = (len(longterm_improved) / len(bl_positive_patients)) * 100
                    else:
                        longterm_improvement_rate = 0
                    
                    # Maintenance rate (Post-LP to Year 1 among those improved at Post-LP)
                    postlp_improved_patients = three_point_data[
                        (three_point_data[bl_col] == 1) & (three_point_data[postlp_col] == 0)
                    ]
                    if len(postlp_improved_patients) > 0:
                        maintained_patients = postlp_improved_patients[postlp_improved_patients[year1_col] == 0]
                        maintenance_rate = (len(maintained_patients) / len(postlp_improved_patients)) * 100
                    else:
                        maintenance_rate = 0
                    
                    # Relapse rate (Post-LP to Year 1 among those improved at Post-LP)
                    relapse_rate = 100 - maintenance_rate if len(postlp_improved_patients) > 0 else 0
                    
                    # Late improvement rate (Post-LP to Year 1 among those not improved at Post-LP)
                    postlp_not_improved = three_point_data[
                        (three_point_data[bl_col] == 1) & (three_point_data[postlp_col] == 1)
                    ]
                    if len(postlp_not_improved) > 0:
                        late_improved = postlp_not_improved[postlp_not_improved[year1_col] == 0]
                        late_improvement_rate = (len(late_improved) / len(postlp_not_improved)) * 100
                    else:
                        late_improvement_rate = 0
                    
                    # Overall relative reduction from baseline to Year 1
                    if bl_positive > 0:
                        overall_relative_reduction = ((bl_positive - year1_positive) / bl_positive) * 100
                    else:
                        overall_relative_reduction = 0
                    
                    results.append({
                        'Drug': drug,
                        'Variable': var,
                        'N_Complete_Followup': total_patients,
                        'Baseline_Positive': bl_positive,
                        'Baseline_Prevalence': bl_prevalence,
                        'PostLP_Positive': postlp_positive,
                        'PostLP_Prevalence': postlp_prevalence,
                        'Year1_Positive': year1_positive,
                        'Year1_Prevalence': year1_prevalence,
                        'Longterm_Improvement_Rate': longterm_improvement_rate,
                        'Maintenance_Rate': maintenance_rate,
                        'Relapse_Rate': relapse_rate,
                        'Late_Improvement_Rate': late_improvement_rate,
                        'Overall_Relative_Reduction': overall_relative_reduction
                    })
                    
                    print(f"\n{var} (Complete 3-timepoint follow-up, n={total_patients}):")
                    print(f"  Baseline prevalence: {bl_prevalence:.1f}% ({bl_positive}/{total_patients})")
                    print(f"  Post-LP prevalence: {postlp_prevalence:.1f}% ({postlp_positive}/{total_patients})")
                    print(f"  Year 1 prevalence: {year1_prevalence:.1f}% ({year1_positive}/{total_patients})")
                    print(f"  Long-term improvement rate (BL→Y1): {longterm_improvement_rate:.1f}%")
                    print(f"  Maintenance rate (Post-LP→Y1): {maintenance_rate:.1f}%")
                    print(f"  Relapse rate (Post-LP→Y1): {relapse_rate:.1f}%")
                    print(f"  Late improvement rate: {late_improvement_rate:.1f}%")
                    print(f"  Overall relative reduction: {overall_relative_reduction:.1f}%")
    
    return pd.DataFrame(results)

def calculate_year1_total_fluid(df):
    """Calculate Year 1 total fluid metrics"""
    print("\n=== Year 1 Total Fluid Analysis (SRF + IRF Combined) ===")
    
    # Create total fluid variables for all timepoints
    timepoints = ['BL', 'Post-LP', 'Year 1']
    
    for tp in timepoints:
        srf_col = f"SRF ({tp})"
        irf_col = f"IRF ({tp})"
        total_fluid_col = f"Total_Fluid ({tp})"
        
        if srf_col in df.columns and irf_col in df.columns:
            df[total_fluid_col] = ((pd.to_numeric(df[srf_col], errors='coerce').fillna(0) == 1) | 
                                  (pd.to_numeric(df[irf_col], errors='coerce').fillna(0) == 1)).astype(int)
    
    total_fluid_results = []
    
    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df[df['Drug'] == drug].copy()
        
        print(f"\n{drug} Group Year 1 Total Fluid Analysis:")
        print("-" * 45)
        
        # Three-timepoint analysis
        fluid_cols = ['Total_Fluid (BL)', 'Total_Fluid (Post-LP)', 'Total_Fluid (Year 1)']
        if all(col in df.columns for col in fluid_cols):
            three_point_data = drug_data[fluid_cols].dropna()
            
            if len(three_point_data) > 0:
                bl_fluid = three_point_data['Total_Fluid (BL)'].sum()
                postlp_fluid = three_point_data['Total_Fluid (Post-LP)'].sum()
                year1_fluid = three_point_data['Total_Fluid (Year 1)'].sum()
                total_patients = len(three_point_data)
                
                bl_prevalence = (bl_fluid / total_patients) * 100
                postlp_prevalence = (postlp_fluid / total_patients) * 100
                year1_prevalence = (year1_fluid / total_patients) * 100
                
                # Long-term dry retina achievement rate
                bl_fluid_patients = three_point_data[three_point_data['Total_Fluid (BL)'] == 1]
                if len(bl_fluid_patients) > 0:
                    longterm_dry = bl_fluid_patients[bl_fluid_patients['Total_Fluid (Year 1)'] == 0]
                    longterm_dry_rate = (len(longterm_dry) / len(bl_fluid_patients)) * 100
                else:
                    longterm_dry_rate = 0
                
                # Maintenance of dry retina (Post-LP to Year 1)
                postlp_dry_patients = three_point_data[three_point_data['Total_Fluid (Post-LP)'] == 0]
                if len(postlp_dry_patients) > 0:
                    maintained_dry = postlp_dry_patients[postlp_dry_patients['Total_Fluid (Year 1)'] == 0]
                    dry_maintenance_rate = (len(maintained_dry) / len(postlp_dry_patients)) * 100
                else:
                    dry_maintenance_rate = 0
                
                # Fluid recurrence rate
                fluid_recurrence_rate = 100 - dry_maintenance_rate if len(postlp_dry_patients) > 0 else 0
                
                total_fluid_results.append({
                    'Drug': drug,
                    'N_Complete_Followup': total_patients,
                    'Baseline_Fluid_Prevalence': bl_prevalence,
                    'PostLP_Fluid_Prevalence': postlp_prevalence,
                    'Year1_Fluid_Prevalence': year1_prevalence,
                    'Longterm_Dry_Retina_Rate': longterm_dry_rate,
                    'Dry_Maintenance_Rate': dry_maintenance_rate,
                    'Fluid_Recurrence_Rate': fluid_recurrence_rate
                })
                
                print(f"  Complete follow-up patients: {total_patients}")
                print(f"  Baseline total fluid: {bl_prevalence:.1f}% ({bl_fluid}/{total_patients})")
                print(f"  Post-LP total fluid: {postlp_prevalence:.1f}% ({postlp_fluid}/{total_patients})")
                print(f"  Year 1 total fluid: {year1_prevalence:.1f}% ({year1_fluid}/{total_patients})")
                print(f"  Long-term dry retina rate (BL→Y1): {longterm_dry_rate:.1f}%")
                print(f"  Dry retina maintenance rate (Post-LP→Y1): {dry_maintenance_rate:.1f}%")
                print(f"  Fluid recurrence rate: {fluid_recurrence_rate:.1f}%")
    
    return pd.DataFrame(total_fluid_results)

def create_year1_visualization(year1_results, total_fluid_year1):
    """Create Year 1 follow-up visualizations"""
    print("\nCreating Year 1 follow-up visualizations...")
    
    # 1. Long-term improvement rates comparison
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # Filter for main morphological indicators
    main_vars = ['IRF', 'SRF', 'SHRM', 'Hemorrhage']
    plot_data = year1_results[year1_results['Variable'].isin(main_vars)]
    
    # Long-term improvement rates
    if not plot_data.empty:
        longterm_data = plot_data.pivot(index='Variable', columns='Drug', values='Longterm_Improvement_Rate')
        longterm_data.plot(kind='bar', ax=axes[0,0], color=['#2E86AB', '#A23B72'])
        axes[0,0].set_title('Long-term Improvement Rates\n(Baseline to Year 1)', fontweight='bold')
        axes[0,0].set_ylabel('Improvement Rate (%)')
        axes[0,0].legend(title='Treatment')
        axes[0,0].tick_params(axis='x', rotation=45)
        
        # Maintenance rates
        maintenance_data = plot_data.pivot(index='Variable', columns='Drug', values='Maintenance_Rate')
        maintenance_data.plot(kind='bar', ax=axes[0,1], color=['#2E86AB', '#A23B72'])
        axes[0,1].set_title('Maintenance Rates\n(Post-LP to Year 1)', fontweight='bold')
        axes[0,1].set_ylabel('Maintenance Rate (%)')
        axes[0,1].legend(title='Treatment')
        axes[0,1].tick_params(axis='x', rotation=45)
        
        # Relapse rates
        relapse_data = plot_data.pivot(index='Variable', columns='Drug', values='Relapse_Rate')
        relapse_data.plot(kind='bar', ax=axes[1,0], color=['#2E86AB', '#A23B72'])
        axes[1,0].set_title('Relapse Rates\n(Post-LP to Year 1)', fontweight='bold')
        axes[1,0].set_ylabel('Relapse Rate (%)')
        axes[1,0].legend(title='Treatment')
        axes[1,0].tick_params(axis='x', rotation=45)
    
    # Total fluid analysis
    if not total_fluid_year1.empty:
        x = np.arange(len(total_fluid_year1))
        width = 0.25
        
        baseline = total_fluid_year1['Baseline_Fluid_Prevalence']
        postlp = total_fluid_year1['PostLP_Fluid_Prevalence']
        year1 = total_fluid_year1['Year1_Fluid_Prevalence']
        drugs = total_fluid_year1['Drug']
        
        axes[1,1].bar(x - width, baseline, width, label='Baseline', alpha=0.8)
        axes[1,1].bar(x, postlp, width, label='Post-LP', alpha=0.8)
        axes[1,1].bar(x + width, year1, width, label='Year 1', alpha=0.8)
        
        axes[1,1].set_title('Total Fluid Prevalence Over Time', fontweight='bold')
        axes[1,1].set_ylabel('Prevalence (%)')
        axes[1,1].set_xlabel('Treatment Group')
        axes[1,1].set_xticks(x)
        axes[1,1].set_xticklabels(drugs)
        axes[1,1].legend()
        
        # Add long-term dry retina rates as text
        for i, (drug, rate) in enumerate(zip(drugs, total_fluid_year1['Longterm_Dry_Retina_Rate'])):
            axes[1,1].text(i, max(baseline.max(), postlp.max(), year1.max()) + 5, 
                          f'Long-term Dry: {rate:.1f}%', 
                          ha='center', va='bottom', fontweight='bold', fontsize=10)
    
    plt.tight_layout()
    plt.savefig('Figure7_Year1_Morphology_Analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Year 1 morphological analysis plot saved: Figure7_Year1_Morphology_Analysis.png")

def statistical_comparison_year1(year1_results, total_fluid_year1):
    """Perform statistical comparisons for Year 1 data"""
    print("\n=== Year 1 Statistical Comparisons ===")
    
    comparison_results = []
    
    # Note about sample size imbalance
    print("⚠️  IMPORTANT NOTE: Year 1 follow-up data shows significant imbalance:")
    print("   EYLEA: 81/86 patients (94.2% follow-up)")
    print("   FARICIMAB: 46/86 patients (53.5% follow-up)")
    print("   This imbalance may affect the validity of statistical comparisons.\n")
    
    # Compare long-term improvement rates
    main_vars = ['IRF', 'SRF', 'SHRM', 'Hemorrhage']
    
    for var in main_vars:
        var_data = year1_results[year1_results['Variable'] == var]
        
        if len(var_data) == 2:
            eylea_data = var_data[var_data['Drug'] == 'EYLEA'].iloc[0]
            faricimab_data = var_data[var_data['Drug'] == 'FARICIMAB'].iloc[0]
            
            comparison_results.append({
                'Variable': var,
                'Metric': 'Longterm_Improvement_Rate',
                'EYLEA_Value': eylea_data['Longterm_Improvement_Rate'],
                'EYLEA_N': eylea_data['N_Complete_Followup'],
                'FARICIMAB_Value': faricimab_data['Longterm_Improvement_Rate'],
                'FARICIMAB_N': faricimab_data['N_Complete_Followup'],
                'Difference': faricimab_data['Longterm_Improvement_Rate'] - eylea_data['Longterm_Improvement_Rate']
            })
            
            print(f"{var} Long-term Improvement Rate (BL→Y1):")
            print(f"  EYLEA: {eylea_data['Longterm_Improvement_Rate']:.1f}% (n={eylea_data['N_Complete_Followup']})")
            print(f"  FARICIMAB: {faricimab_data['Longterm_Improvement_Rate']:.1f}% (n={faricimab_data['N_Complete_Followup']})")
            print(f"  Difference: {faricimab_data['Longterm_Improvement_Rate'] - eylea_data['Longterm_Improvement_Rate']:+.1f} percentage points")
            print(f"  ⚠️  Statistical testing not performed due to sample size imbalance\n")
    
    return pd.DataFrame(comparison_results)

if __name__ == "__main__":
    # Load data
    df = load_data()
    
    # Analyze Year 1 data availability
    year1_availability = analyze_year1_morphology(df)
    
    # Detailed Year 1 metrics
    year1_results = calculate_year1_detailed_metrics(df)
    
    # Year 1 total fluid analysis
    total_fluid_year1 = calculate_year1_total_fluid(df)
    
    # Statistical comparisons
    year1_comparisons = statistical_comparison_year1(year1_results, total_fluid_year1)
    
    # Create visualizations
    create_year1_visualization(year1_results, total_fluid_year1)
    
    # Save results
    year1_results.to_csv('Table6_Year1_Detailed_Morphology.csv', index=False)
    total_fluid_year1.to_csv('Table7_Year1_Total_Fluid.csv', index=False)
    year1_comparisons.to_csv('Table8_Year1_Comparisons.csv', index=False)
    
    print(f"\nYear 1 morphological analysis completed!")
    print("Generated files:")
    print("- Table6_Year1_Detailed_Morphology.csv")
    print("- Table7_Year1_Total_Fluid.csv")
    print("- Table8_Year1_Comparisons.csv")
    print("- Figure7_Year1_Morphology_Analysis.png")
    
    print(f"\n⚠️  IMPORTANT LIMITATION:")
    print(f"Year 1 follow-up data shows significant imbalance between groups.")
    print(f"Results should be interpreted with caution due to potential selection bias.")
