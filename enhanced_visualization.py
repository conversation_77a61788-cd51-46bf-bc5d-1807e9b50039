#!/usr/bin/env python3
"""
Enhanced Visualization Script for nAMD Analysis
Adds error bars and p-values to all bar charts
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import chi2_contingency, fisher_exact
import warnings
warnings.filterwarnings('ignore')

# Set style
plt.style.use('default')
sns.set_palette("husl")

def calculate_proportion_ci(successes, total, confidence=0.95):
    """Calculate confidence interval for proportion using Wilson score interval"""
    if total == 0:
        return 0, 0, 0
    
    p = successes / total
    z = stats.norm.ppf((1 + confidence) / 2)
    
    # Wilson score interval
    denominator = 1 + z**2 / total
    centre_adjusted_probability = (p + z**2 / (2 * total)) / denominator
    adjusted_standard_deviation = np.sqrt((p * (1 - p) + z**2 / (4 * total)) / total) / denominator
    
    lower_bound = centre_adjusted_probability - z * adjusted_standard_deviation
    upper_bound = centre_adjusted_probability + z * adjusted_standard_deviation
    
    return p * 100, lower_bound * 100, upper_bound * 100

def calculate_statistical_significance(data, var_name, drug1='EYLEA', drug2='FARICIMAB'):
    """Calculate p-value for comparison between two groups"""
    try:
        # Get data for both groups
        group1_data = data[(data['Drug'] == drug1) & (data['Variable'] == var_name)]
        group2_data = data[(data['Drug'] == drug2) & (data['Variable'] == var_name)]
        
        if group1_data.empty or group2_data.empty:
            return None
        
        # For proportion data, use chi-square test or Fisher's exact test
        if 'Baseline_Positive' in group1_data.columns and 'PostLP_Positive' in group1_data.columns:
            # Create contingency table for improvement
            g1_improved = group1_data['Baseline_Positive'].iloc[0] - group1_data['PostLP_Positive'].iloc[0]
            g1_not_improved = group1_data['PostLP_Positive'].iloc[0]
            g2_improved = group2_data['Baseline_Positive'].iloc[0] - group2_data['PostLP_Positive'].iloc[0]
            g2_not_improved = group2_data['PostLP_Positive'].iloc[0]
            
            contingency_table = np.array([[g1_improved, g1_not_improved],
                                        [g2_improved, g2_not_improved]])
            
            # Use Fisher's exact test for small samples
            if np.any(contingency_table < 5):
                _, p_value = fisher_exact(contingency_table)
            else:
                chi2, p_value, _, _ = chi2_contingency(contingency_table)
            
            return p_value
        
    except Exception as e:
        print(f"Error calculating p-value for {var_name}: {e}")
        return None
    
    return None

def add_significance_bar(ax, x1, x2, y, p_value, height_offset=2):
    """Add significance bar and p-value annotation to plot"""
    if p_value is None:
        return
    
    # Determine significance level
    if p_value < 0.001:
        sig_text = '***'
    elif p_value < 0.01:
        sig_text = '**'
    elif p_value < 0.05:
        sig_text = '*'
    else:
        sig_text = 'ns'
    
    # Add horizontal line
    ax.plot([x1, x2], [y, y], 'k-', linewidth=1)
    # Add vertical lines
    ax.plot([x1, x1], [y-height_offset/2, y], 'k-', linewidth=1)
    ax.plot([x2, x2], [y-height_offset/2, y], 'k-', linewidth=1)
    
    # Add p-value text
    ax.text((x1 + x2) / 2, y + height_offset/2, f'p={p_value:.3f}\n{sig_text}', 
            ha='center', va='bottom', fontsize=9, fontweight='bold')

def create_enhanced_morphology_plots(data_file):
    """Create enhanced morphological plots with error bars and p-values"""
    print("Creating enhanced morphological plots with error bars and p-values...")

    # Load data
    df = pd.read_csv(data_file)

    # Prepare morphological data - use actual column names from the dataset
    morphology_vars = ['IRF', 'SRF', 'SHRM', 'Hemorrhage']

    # Calculate improvement rates with confidence intervals
    results = []

    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = df[df['Drug'] == drug]

        for var in morphology_vars:
            baseline_col = f'{var} (BL)'
            postlp_col = f'{var} (Post-LP)'

            if baseline_col in df.columns and postlp_col in df.columns:
                baseline_positive = (drug_data[baseline_col] == 1).sum()
                postlp_positive = (drug_data[postlp_col] == 1).sum()
                total_patients = len(drug_data)

                # Calculate improvement (baseline positive -> post-LP negative)
                improved = baseline_positive - postlp_positive
                if baseline_positive > 0:
                    improvement_rate, ci_lower, ci_upper = calculate_proportion_ci(improved, baseline_positive)
                    resolution_rate, res_ci_lower, res_ci_upper = calculate_proportion_ci(
                        baseline_positive - postlp_positive, baseline_positive)
                else:
                    improvement_rate = ci_lower = ci_upper = 0
                    resolution_rate = res_ci_lower = res_ci_upper = 0

                results.append({
                    'Drug': drug,
                    'Variable': var,
                    'Improvement_Rate': improvement_rate,
                    'Improvement_CI_Lower': ci_lower,
                    'Improvement_CI_Upper': ci_upper,
                    'Resolution_Rate': resolution_rate,
                    'Resolution_CI_Lower': res_ci_lower,
                    'Resolution_CI_Upper': res_ci_upper,
                    'Baseline_Positive': baseline_positive,
                    'PostLP_Positive': postlp_positive,
                    'Total_Patients': total_patients
                })

    results_df = pd.DataFrame(results)

    if results_df.empty:
        print("No data available for morphological analysis")
        return results_df
    
    # Create enhanced plots
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. Improvement rates with error bars and p-values
    ax = axes[0, 0]
    plot_data = results_df.pivot(index='Variable', columns='Drug', values='Improvement_Rate')
    ci_lower = results_df.pivot(index='Variable', columns='Drug', values='Improvement_CI_Lower')
    ci_upper = results_df.pivot(index='Variable', columns='Drug', values='Improvement_CI_Upper')
    
    x = np.arange(len(morphology_vars))
    width = 0.35
    
    eylea_rates = [plot_data.loc[var, 'EYLEA'] if var in plot_data.index else 0 for var in morphology_vars]
    faricimab_rates = [plot_data.loc[var, 'FARICIMAB'] if var in plot_data.index else 0 for var in morphology_vars]
    
    eylea_errors = [[plot_data.loc[var, 'EYLEA'] - ci_lower.loc[var, 'EYLEA'] if var in plot_data.index else 0 for var in morphology_vars],
                    [ci_upper.loc[var, 'EYLEA'] - plot_data.loc[var, 'EYLEA'] if var in plot_data.index else 0 for var in morphology_vars]]
    
    faricimab_errors = [[plot_data.loc[var, 'FARICIMAB'] - ci_lower.loc[var, 'FARICIMAB'] if var in plot_data.index else 0 for var in morphology_vars],
                        [ci_upper.loc[var, 'FARICIMAB'] - plot_data.loc[var, 'FARICIMAB'] if var in plot_data.index else 0 for var in morphology_vars]]
    
    bars1 = ax.bar(x - width/2, eylea_rates, width, label='EYLEA', 
                   color='#2E86AB', alpha=0.8, yerr=eylea_errors, capsize=5)
    bars2 = ax.bar(x + width/2, faricimab_rates, width, label='FARICIMAB', 
                   color='#A23B72', alpha=0.8, yerr=faricimab_errors, capsize=5)
    
    ax.set_title('Improvement Rates by Morphological Indicator\n(with 95% Confidence Intervals)', fontweight='bold')
    ax.set_ylabel('Improvement Rate (%)')
    ax.set_xlabel('Morphological Indicator')
    ax.set_xticks(x)
    ax.set_xticklabels(morphology_vars)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # Add p-values
    max_height = max(max(eylea_rates), max(faricimab_rates)) + 10
    for i, var in enumerate(morphology_vars):
        p_value = calculate_statistical_significance(results_df, var)
        if p_value is not None:
            add_significance_bar(ax, i - width/2, i + width/2, max_height + i*5, p_value)
    
    plt.tight_layout()
    plt.savefig('Enhanced_Figure_Morphology_Analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

    return results_df

def create_enhanced_year1_plots(data_file):
    """Create enhanced Year 1 follow-up plots with error bars and p-values"""
    print("Creating enhanced Year 1 follow-up plots...")

    # Load data
    df = pd.read_csv(data_file)

    # Filter for patients with Year 1 data - use actual column names
    year1_data = df.dropna(subset=['IRF (Year 1)', 'SRF (Year 1)', 'SHRM (Year 1)'])

    print(f"Year 1 follow-up data available:")
    for drug in ['EYLEA', 'FARICIMAB']:
        count = len(year1_data[year1_data['Drug'] == drug])
        total = len(df[df['Drug'] == drug])
        print(f"  {drug}: {count}/{total} ({count/total*100:.1f}%)")

    # Calculate Year 1 metrics with confidence intervals
    morphology_vars = ['IRF', 'SRF', 'SHRM', 'Hemorrhage']
    year1_results = []

    for drug in ['EYLEA', 'FARICIMAB']:
        drug_data = year1_data[year1_data['Drug'] == drug]

        for var in morphology_vars:
            baseline_col = f'{var} (BL)'
            postlp_col = f'{var} (Post-LP)'
            year1_col = f'{var} (Year 1)'

            if all(col in df.columns for col in [baseline_col, postlp_col, year1_col]):
                # Long-term improvement (Baseline -> Year 1)
                baseline_positive = (drug_data[baseline_col] == 1).sum()
                year1_positive = (drug_data[year1_col] == 1).sum()
                longterm_improved = baseline_positive - year1_positive

                if baseline_positive > 0:
                    longterm_rate, lt_ci_lower, lt_ci_upper = calculate_proportion_ci(
                        longterm_improved, baseline_positive)
                else:
                    longterm_rate = lt_ci_lower = lt_ci_upper = 0

                # Maintenance rate (Post-LP -> Year 1)
                postlp_negative = (drug_data[postlp_col] == 0).sum()
                year1_negative = (drug_data[year1_col] == 0).sum()
                maintained = min(postlp_negative, year1_negative)

                if postlp_negative > 0:
                    maintenance_rate, maint_ci_lower, maint_ci_upper = calculate_proportion_ci(
                        maintained, postlp_negative)
                else:
                    maintenance_rate = maint_ci_lower = maint_ci_upper = 0

                year1_results.append({
                    'Drug': drug,
                    'Variable': var,
                    'Longterm_Improvement_Rate': longterm_rate,
                    'Longterm_CI_Lower': lt_ci_lower,
                    'Longterm_CI_Upper': lt_ci_upper,
                    'Maintenance_Rate': maintenance_rate,
                    'Maintenance_CI_Lower': maint_ci_lower,
                    'Maintenance_CI_Upper': maint_ci_upper,
                    'Baseline_Positive': baseline_positive,
                    'Year1_Positive': year1_positive,
                    'PostLP_Negative': postlp_negative
                })

    year1_df = pd.DataFrame(year1_results)

    # Create enhanced Year 1 plots
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))

    # 1. Long-term improvement rates
    ax = axes[0, 0]
    plot_data = year1_df.pivot(index='Variable', columns='Drug', values='Longterm_Improvement_Rate')
    ci_lower = year1_df.pivot(index='Variable', columns='Drug', values='Longterm_CI_Lower')
    ci_upper = year1_df.pivot(index='Variable', columns='Drug', values='Longterm_CI_Upper')

    x = np.arange(len(morphology_vars))
    width = 0.35

    eylea_rates = [plot_data.loc[var, 'EYLEA'] if var in plot_data.index else 0 for var in morphology_vars]
    faricimab_rates = [plot_data.loc[var, 'FARICIMAB'] if var in plot_data.index else 0 for var in morphology_vars]

    eylea_errors = [[plot_data.loc[var, 'EYLEA'] - ci_lower.loc[var, 'EYLEA'] if var in plot_data.index else 0 for var in morphology_vars],
                    [ci_upper.loc[var, 'EYLEA'] - plot_data.loc[var, 'EYLEA'] if var in plot_data.index else 0 for var in morphology_vars]]

    faricimab_errors = [[plot_data.loc[var, 'FARICIMAB'] - ci_lower.loc[var, 'FARICIMAB'] if var in plot_data.index else 0 for var in morphology_vars],
                        [ci_upper.loc[var, 'FARICIMAB'] - plot_data.loc[var, 'FARICIMAB'] if var in plot_data.index else 0 for var in morphology_vars]]

    bars1 = ax.bar(x - width/2, eylea_rates, width, label='EYLEA',
                   color='#2E86AB', alpha=0.8, yerr=eylea_errors, capsize=5)
    bars2 = ax.bar(x + width/2, faricimab_rates, width, label='FARICIMAB',
                   color='#A23B72', alpha=0.8, yerr=faricimab_errors, capsize=5)

    ax.set_title('Long-term Improvement Rates (Baseline to Year 1)\n(with 95% Confidence Intervals)', fontweight='bold')
    ax.set_ylabel('Improvement Rate (%)')
    ax.set_xlabel('Morphological Indicator')
    ax.set_xticks(x)
    ax.set_xticklabels(morphology_vars)
    ax.legend()
    ax.grid(True, alpha=0.3)

    # Add p-values for long-term improvement
    max_height = max(max(eylea_rates), max(faricimab_rates)) + 10
    for i, var in enumerate(morphology_vars):
        # Calculate p-value for long-term improvement comparison
        eylea_data = year1_df[(year1_df['Drug'] == 'EYLEA') & (year1_df['Variable'] == var)]
        faricimab_data = year1_df[(year1_df['Drug'] == 'FARICIMAB') & (year1_df['Variable'] == var)]

        if not eylea_data.empty and not faricimab_data.empty:
            # Create contingency table for Fisher's exact test
            e_improved = eylea_data['Baseline_Positive'].iloc[0] - eylea_data['Year1_Positive'].iloc[0]
            e_not_improved = eylea_data['Year1_Positive'].iloc[0]
            f_improved = faricimab_data['Baseline_Positive'].iloc[0] - faricimab_data['Year1_Positive'].iloc[0]
            f_not_improved = faricimab_data['Year1_Positive'].iloc[0]

            if e_improved + e_not_improved > 0 and f_improved + f_not_improved > 0:
                contingency_table = np.array([[e_improved, e_not_improved],
                                            [f_improved, f_not_improved]])
                try:
                    _, p_value = fisher_exact(contingency_table)
                    add_significance_bar(ax, i - width/2, i + width/2, max_height + i*8, p_value)
                except:
                    pass

    # 2. Maintenance rates
    ax = axes[0, 1]
    maint_data = year1_df.pivot(index='Variable', columns='Drug', values='Maintenance_Rate')
    maint_ci_lower = year1_df.pivot(index='Variable', columns='Drug', values='Maintenance_CI_Lower')
    maint_ci_upper = year1_df.pivot(index='Variable', columns='Drug', values='Maintenance_CI_Upper')

    eylea_maint = [maint_data.loc[var, 'EYLEA'] if var in maint_data.index else 0 for var in morphology_vars]
    faricimab_maint = [maint_data.loc[var, 'FARICIMAB'] if var in maint_data.index else 0 for var in morphology_vars]

    eylea_maint_errors = [[maint_data.loc[var, 'EYLEA'] - maint_ci_lower.loc[var, 'EYLEA'] if var in maint_data.index else 0 for var in morphology_vars],
                          [maint_ci_upper.loc[var, 'EYLEA'] - maint_data.loc[var, 'EYLEA'] if var in maint_data.index else 0 for var in morphology_vars]]

    faricimab_maint_errors = [[maint_data.loc[var, 'FARICIMAB'] - maint_ci_lower.loc[var, 'FARICIMAB'] if var in maint_data.index else 0 for var in morphology_vars],
                              [maint_ci_upper.loc[var, 'FARICIMAB'] - maint_data.loc[var, 'FARICIMAB'] if var in maint_data.index else 0 for var in morphology_vars]]

    bars1 = ax.bar(x - width/2, eylea_maint, width, label='EYLEA',
                   color='#2E86AB', alpha=0.8, yerr=eylea_maint_errors, capsize=5)
    bars2 = ax.bar(x + width/2, faricimab_maint, width, label='FARICIMAB',
                   color='#A23B72', alpha=0.8, yerr=faricimab_maint_errors, capsize=5)

    ax.set_title('Maintenance Rates (Post-LP to Year 1)\n(with 95% Confidence Intervals)', fontweight='bold')
    ax.set_ylabel('Maintenance Rate (%)')
    ax.set_xlabel('Morphological Indicator')
    ax.set_xticks(x)
    ax.set_xticklabels(morphology_vars)
    ax.legend()
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('Enhanced_Figure7_Year1_Morphology_Analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

    return year1_df

if __name__ == "__main__":
    # Load and process data
    data_file = "nAMD naive LP of eylea & faricimab 02-07-2025-2（lorena补充完临床资料）.csv"

    print("=== Creating Enhanced Morphological Analysis Plots ===")
    results = create_enhanced_morphology_plots(data_file)

    print("\n=== Creating Enhanced Year 1 Follow-up Plots ===")
    year1_results = create_enhanced_year1_plots(data_file)

    print("\nEnhanced visualization completed!")
    print("Generated files:")
    print("- Enhanced_Figure_Morphology_Analysis.png")
    print("- Enhanced_Figure7_Year1_Morphology_Analysis.png")
