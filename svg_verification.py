#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SVG Files Verification and Summary
Checks all generated SVG files and their properties
"""

import os
import glob
from datetime import datetime
import xml.etree.ElementTree as ET

def analyze_svg_file(svg_file):
    """Analyze SVG file properties"""
    try:
        # Parse SVG file
        tree = ET.parse(svg_file)
        root = tree.getroot()
        
        # Get SVG dimensions
        width = root.get('width', 'Unknown')
        height = root.get('height', 'Unknown')
        viewbox = root.get('viewBox', 'Unknown')
        
        # Count text elements
        text_elements = len(root.findall('.//{http://www.w3.org/2000/svg}text'))
        
        # Count path elements (for graphics)
        path_elements = len(root.findall('.//{http://www.w3.org/2000/svg}path'))
        
        # Count group elements
        group_elements = len(root.findall('.//{http://www.w3.org/2000/svg}g'))
        
        return {
            'width': width,
            'height': height,
            'viewbox': viewbox,
            'text_elements': text_elements,
            'path_elements': path_elements,
            'group_elements': group_elements,
            'valid': True
        }
    except Exception as e:
        return {
            'error': str(e),
            'valid': False
        }

def get_file_info(filename):
    """Get file size and modification time"""
    if os.path.exists(filename):
        size_kb = os.path.getsize(filename) / 1024
        mod_time = datetime.fromtimestamp(os.path.getmtime(filename))
        return size_kb, mod_time
    return None, None

def main():
    print("="*80)
    print("🎨 SVG 文件验证和分析报告")
    print("="*80)
    
    # Find all SVG files
    svg_files = glob.glob('*.svg')
    analysis_svgs = [f for f in svg_files if any(keyword in f for keyword in 
                    ['Enhanced', 'Morphology', 'Timeline', 'MNV', 'Drug', 'Test'])]
    
    if not analysis_svgs:
        print("❌ 未找到SVG文件")
        return
    
    print(f"\n📊 找到 {len(analysis_svgs)} 个SVG文件:")
    print("-" * 80)
    
    total_size = 0
    valid_files = 0
    
    for svg_file in sorted(analysis_svgs):
        size_kb, mod_time = get_file_info(svg_file)
        if size_kb is not None:
            total_size += size_kb
            
            print(f"\n📄 {svg_file}")
            print(f"   📏 大小: {size_kb:.1f} KB")
            print(f"   🕒 修改时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Analyze SVG content
            svg_info = analyze_svg_file(svg_file)
            if svg_info['valid']:
                valid_files += 1
                print(f"   ✅ SVG结构: 有效")
                print(f"   📐 尺寸: {svg_info['width']} × {svg_info['height']}")
                if svg_info['viewbox'] != 'Unknown':
                    print(f"   🔍 ViewBox: {svg_info['viewbox']}")
                print(f"   📝 文本元素: {svg_info['text_elements']} 个")
                print(f"   🎨 路径元素: {svg_info['path_elements']} 个")
                print(f"   📦 分组元素: {svg_info['group_elements']} 个")
            else:
                print(f"   ❌ SVG解析错误: {svg_info['error']}")
    
    print("\n" + "="*80)
    print("✅ SVG 格式优势总结")
    print("="*80)
    
    print("\n🎯 SVG格式特点:")
    print("✅ 矢量图形，无限缩放不失真")
    print("✅ 基于XML，可用文本编辑器编辑")
    print("✅ 支持CSS样式和JavaScript交互")
    print("✅ 网页原生支持，无需插件")
    print("✅ 文件通常比位图格式更小")
    
    print("\n🔧 编辑工具推荐:")
    print("1. 🎨 Adobe Illustrator - 专业矢量图形编辑")
    print("2. 🆓 Inkscape - 免费开源矢量编辑器")
    print("3. 📝 任何文本编辑器 - 直接编辑XML代码")
    print("4. 🌐 浏览器 - 直接查看和调试")
    print("5. 💻 VS Code - 带SVG预览插件")
    
    print("\n📊 文件统计:")
    print(f"- SVG文件总数: {len(analysis_svgs)} 个")
    print(f"- 有效文件: {valid_files} 个")
    print(f"- 总文件大小: {total_size:.1f} KB")
    print(f"- 平均文件大小: {total_size/len(analysis_svgs):.1f} KB")
    
    print("\n🎨 使用场景:")
    print("- 📱 响应式网页设计")
    print("- 📄 高质量印刷品")
    print("- 🎯 图标和Logo设计")
    print("- 📊 交互式数据可视化")
    print("- 📖 学术论文和演示")
    
    print("\n💡 技术优势:")
    print("- 🔤 文本完全可编辑和可搜索")
    print("- 🎨 支持渐变、滤镜等高级效果")
    print("- 📱 自适应不同屏幕分辨率")
    print("- 🔗 可嵌入超链接和元数据")
    print("- ⚡ 加载速度快，渲染效率高")
    
    # List key files
    key_files = [
        'Enhanced_Morphology_Comparison.svg',
        'Enhanced_Year1_Comparison.svg',
        'MNV_Subgroup_Timeline_Analysis.svg',
        'Drug_MNV1_Timeline_Analysis.svg',
        'Drug_MNV2_Timeline_Analysis.svg',
        'Drug_MNV3_Timeline_Analysis.svg'
    ]
    
    print("\n📋 主要分析文件:")
    for i, filename in enumerate(key_files, 1):
        if filename in analysis_svgs:
            print(f"{i}. ✅ {filename}")
        else:
            print(f"{i}. ❌ {filename} (未找到)")
    
    print(f"\n🎉 所有图表现在提供三种格式:")
    print("- 📸 PNG: 快速预览和网页显示")
    print("- 📄 PDF: Adobe Acrobat编辑和学术发表")
    print("- 🎨 SVG: 矢量编辑和网页嵌入")

if __name__ == "__main__":
    main()
